"use client";

import { useState, useEffect, useCallback, memo } from 'react';
import { motion, useAnimation } from 'framer-motion';

/**
 * Helper function to detect low-performance devices
 */
const isLowPerformanceDevice = () => {
  if (typeof window === 'undefined') return false;
  
  // Check for mobile devices
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
  
  // Check for low-end device indicators
  const hardwareConcurrency = navigator.hardwareConcurrency || 0;
  const isLowEndDevice = hardwareConcurrency > 0 && hardwareConcurrency <= 4;
  
  // Check for battery status if available
  let isBatteryLow = false;
  if ('getBattery' in navigator) {
    // Don't await this since it's just an optimization
    navigator.getBattery().then(battery => {
      isBatteryLow = battery.level <= 0.2 && !battery.charging;
    }).catch(() => {});
  }
  
  return isMobile || isLowEndDevice || isBatteryLow;
};

/**
 * Debounce function to limit how often a function is called
 */
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * FallingIcons component - optimized for performance
 * Creates a background animation of falling icons/particles
 */
function FallingIcons({ count = 20, icons = ['✉️', '📱', '💬', '🔔', '📞', '🗓️', '📊', '💼'] }) {
  const [particles, setParticles] = useState([]);
  const [isPaused, setIsPaused] = useState(false);
  const controls = useAnimation();

  // Generate particles based on device performance
  const generateParticles = useCallback(() => {
    const newParticles = [];
    const isLowPerformance = isLowPerformanceDevice();
    
    // Reduce particle count for low-performance devices
    const adjustedCount = isLowPerformance ? Math.floor(count / 2) : count;
    const actualCount = Math.min(adjustedCount, Math.floor(window.innerWidth / 100));
    
    for (let i = 0; i < actualCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100, // random x position (0-100%)
        y: -10 - Math.random() * 100, // start above viewport
        size: isLowPerformance ? (12 + Math.random() * 12) : (16 + Math.random() * 24), // smaller on low-end devices
        duration: isLowPerformance ? (15 + Math.random() * 10) : (10 + Math.random() * 20), // slower on low-end devices
        delay: Math.random() * 5, // random delay (0-5s)
        rotate: Math.random() * 360, // random initial rotation
        icon: icons[Math.floor(Math.random() * icons.length)], // random icon
        opacity: isLowPerformance ? (0.1 + Math.random() * 0.2) : (0.1 + Math.random() * 0.3), // lower opacity on low-end
      });
    }
    return newParticles;
  }, [count, icons]);

  // Effect to handle page visibility changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        setIsPaused(true);
        controls.stop();
      } else {
        setIsPaused(false);
        controls.start();
      }
    };

    const handleScroll = debounce(() => {
      setIsPaused(true);
      controls.stop();
      
      // Resume animations after scrolling stops
      setTimeout(() => {
        setIsPaused(false);
        controls.start();
      }, 200);
    }, 100);

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [controls]);

  // Generate particles and handle resize
  useEffect(() => {
    // Only run on client
    if (typeof window === 'undefined') return;

    setParticles(generateParticles());

    // Regenerate particles on window resize with debounce
    const handleResize = debounce(() => {
      setParticles(generateParticles());
    }, 200);

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [count, icons]); // Fixed: Use primitive dependencies instead of function

  if (particles.length === 0) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute text-white/30 select-none will-change-transform"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            fontSize: `${particle.size}px`,
          }}
          initial={{ 
            y: particle.y, 
            x: particle.x,
            rotate: particle.rotate,
            opacity: 0
          }}
          animate={isPaused ? {} : { 
            y: '120vh', 
            rotate: particle.rotate + (Math.random() > 0.5 ? 360 : -360),
            opacity: [0, particle.opacity, particle.opacity, 0]
          }}
          transition={{ 
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: 'linear'
          }}
        >
          {particle.icon}
        </motion.div>
      ))}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(FallingIcons);
