"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_ToolsIntegrationSection_jsx"],{

/***/ "(app-pages-browser)/./app/components/ToolsIntegrationSection.jsx":
/*!****************************************************!*\
  !*** ./app/components/ToolsIntegrationSection.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ToolsIntegrationSection = ()=>{\n    const integrations = [\n        {\n            name: \"Google Calendar\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg\"\n        },\n        {\n            name: \"Outlook\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/df/Microsoft_Office_Outlook_%282018%E2%80%93present%29.svg\"\n        },\n        {\n            name: \"Slack\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg\"\n        },\n        {\n            name: \"Salesforce\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg\"\n        },\n        {\n            name: \"Zapier\",\n            logo: \"https://cdn.worldvectorlogo.com/logos/zapier-1.svg\"\n        },\n        {\n            name: \"HubSpot\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/4/4d/HubSpot_Logo.svg\"\n        },\n        {\n            name: \"Zoom\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/1/11/Zoom_Logo_2022.svg\"\n        },\n        {\n            name: \"Shopify\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/0/0e/Shopify_logo_2018.svg\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-16 px-4 text-white scroll-reveal relative z-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-40 right-20 w-[30rem] h-[30rem] rounded-full bg-blue-600/5 blur-[100px] animate-pulse-slow\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-40 left-20 w-[30rem] h-[30rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow\",\n                style: {\n                    animationDelay: \"2s\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-4xl mx-auto mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"heading-lg mb-6 laser-gradient-text text-center\",\n                        \"data-text\": \"Tools & Integrations\",\n                        children: \"Tools & Integrations\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"subheading-text\",\n                        children: \"Seamlessly connects with the tools you already use, making it easy to incorporate CallSaver into your existing workflow without disruption.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-md p-8 md:p-10 rounded-xl border border-purple-500/20 max-w-5xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12\",\n                        children: integrations.map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center p-3 mb-4 hover:bg-white/90 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: integration.logo,\n                                                alt: \"\".concat(integration.name, \" logo\"),\n                                                fill: true,\n                                                sizes: \"(max-width: 768px) 64px, 80px\",\n                                                style: {\n                                                    objectFit: \"contain\"\n                                                },\n                                                quality: 90\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-300 text-center\",\n                                        children: integration.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: \"The bigger your ecosystem, the more powerful CallSaver becomes. Connect with all your favorite platforms through our API or integrations.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"#pricing\",\n                                    className: \"inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-white bg-purple-600 hover:bg-purple-700 transition duration-300 ease-in-out transform hover:scale-105\",\n                                    children: \"See All Integrations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ToolsIntegrationSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ToolsIntegrationSection);\nvar _c;\n$RefreshReg$(_c, \"ToolsIntegrationSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ToolsIntegrationSection.jsx\n"));

/***/ })

}]);