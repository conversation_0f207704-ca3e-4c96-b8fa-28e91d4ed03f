"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_ErrorBoundary_jsx"],{

/***/ "(app-pages-browser)/./app/components/ErrorBoundary.jsx":
/*!******************************************!*\
  !*** ./app/components/ErrorBoundary.jsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError(error) {\n        // Update state so the next render will show the fallback UI\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        // You can log the error to an error reporting service\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n        this.setState({\n            errorInfo: errorInfo\n        });\n        // Log to analytics or monitoring service in production\n        if (false) {}\n    }\n    render() {\n        const { hasError, error } = this.state;\n        const { fallback, children, silent = false } = this.props;\n        if (hasError) {\n            var _this_state_errorInfo;\n            // If a fallback is provided, use it\n            if (fallback) {\n                return fallback;\n            }\n            // If silent is true, don't show any error UI\n            if (silent) {\n                return null;\n            }\n            // Default fallback UI\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"error-boundary-fallback\",\n                style: {\n                    padding: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Something went wrong.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: (error === null || error === void 0 ? void 0 : error.message) || \"Unknown error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                        style: {\n                            whiteSpace: \"pre-wrap\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                children: \"Developer Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: error === null || error === void 0 ? void 0 : error.toString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: ((_this_state_errorInfo = this.state.errorInfo) === null || _this_state_errorInfo === void 0 ? void 0 : _this_state_errorInfo.componentStack) || \"No component stack available\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ErrorBoundary.jsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this);\n        }\n        // If there's no error, render children normally\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (ErrorBoundary);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ErrorBoundary.jsx\n"));

/***/ })

}]);