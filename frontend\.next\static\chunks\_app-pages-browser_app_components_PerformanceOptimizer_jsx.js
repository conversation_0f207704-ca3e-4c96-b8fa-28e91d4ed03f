"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_PerformanceOptimizer_jsx"],{

/***/ "(app-pages-browser)/./app/components/PerformanceOptimizer.jsx":
/*!*************************************************!*\
  !*** ./app/components/PerformanceOptimizer.jsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PerformanceOptimizer; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n/**\r\n * Component that implements various performance optimizations\r\n * This includes prefetching, preconnecting, and other performance-related tasks\r\n */ function PerformanceOptimizer() {\n    _s();\n    // Define functions inside useEffect to avoid dependency issues\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        /**\r\n     * Prefetch critical resources for common user flows\r\n     */ const prefetchCriticalResources = ()=>{\n            // Only run on production to avoid unnecessary requests during development\n            if (true) return;\n            // Helper function to prefetch a URL\n            const prefetch = (url)=>{\n                const link = document.createElement(\"link\");\n                link.rel = \"prefetch\";\n                link.href = url;\n                document.head.appendChild(link);\n            };\n            // Prefetch critical paths that users are likely to navigate to\n            setTimeout(()=>{\n                prefetch(\"/dashboard\");\n                prefetch(\"/crm\");\n            // Add more critical paths as needed\n            }, 3000); // Delay prefetching to prioritize initial page load\n        };\n        /**\r\n     * Set up lazy loading for non-critical elements\r\n     */ const setupLazyLoading = ()=>{\n            if ( false || !window.IntersectionObserver) return;\n            // Create an observer for lazy-loaded elements\n            const observer = new IntersectionObserver((entries)=>{\n                entries.forEach((entry)=>{\n                    if (!entry.isIntersecting) return;\n                    const target = entry.target;\n                    // Handle different types of elements\n                    if (target.dataset.lazySrc) {\n                        // For images\n                        target.src = target.dataset.lazySrc;\n                        target.removeAttribute(\"data-lazy-src\");\n                    } else if (target.dataset.lazyBg) {\n                        // For background images\n                        target.style.backgroundImage = \"url(\".concat(target.dataset.lazyBg, \")\");\n                        target.removeAttribute(\"data-lazy-bg\");\n                    } else if (target.dataset.lazyComponent) {\n                        // Load components dynamically - the component should handle its own loading\n                        target.dataset.lazyLoaded = \"true\";\n                        target.removeAttribute(\"data-lazy-component\");\n                    }\n                    // Once loaded, no need to observe anymore\n                    observer.unobserve(target);\n                });\n            }, {\n                rootMargin: \"200px\",\n                threshold: 0.01 // Trigger when at least 1% of the element is visible\n            });\n            // Observe all elements with lazy loading attributes\n            document.querySelectorAll(\"[data-lazy-src], [data-lazy-bg], [data-lazy-component]\").forEach((el)=>{\n                observer.observe(el);\n            });\n            // Clean up observer on unmount\n            return ()=>{\n                observer.disconnect();\n            };\n        };\n        /**\r\n     * Optimize performance when page visibility changes\r\n     */ const setupVisibilityHandling = ()=>{\n            if (typeof document === \"undefined\") return;\n            // Handler function for visibility changes\n            const handleVisibilityChange = ()=>{\n                if (document.hidden) {\n                    // Page is not visible\n                    document.body.classList.add(\"performance-paused\");\n                    // Signal to other components that page is hidden\n                    window.dispatchEvent(new CustomEvent(\"callsaver:visibility-hidden\"));\n                } else {\n                    // Page is visible again\n                    document.body.classList.remove(\"performance-paused\");\n                    // Signal to other components that page is visible\n                    window.dispatchEvent(new CustomEvent(\"callsaver:visibility-visible\"));\n                }\n            };\n            // Add event listener\n            document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n            // Clean up event listener on unmount\n            return ()=>{\n                document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n            };\n        };\n        // Execute the optimization functions\n        prefetchCriticalResources();\n        const lazyLoadingCleanup = setupLazyLoading();\n        const visibilityHandlingCleanup = setupVisibilityHandling();\n        // Return a cleanup function that calls all the individual cleanup functions\n        return ()=>{\n            if (lazyLoadingCleanup) lazyLoadingCleanup();\n            if (visibilityHandlingCleanup) visibilityHandlingCleanup();\n        };\n    }, []);\n    // Functions moved inside useEffect to avoid dependency issues\n    // This component doesn't render anything visible\n    return null;\n}\n_s(PerformanceOptimizer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = PerformanceOptimizer;\nvar _c;\n$RefreshReg$(_c, \"PerformanceOptimizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/PerformanceOptimizer.jsx\n"));

/***/ })

}]);