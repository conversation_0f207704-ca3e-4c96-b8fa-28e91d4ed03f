"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_CallPreviewBox_jsx"],{

/***/ "(app-pages-browser)/./app/components/CallPreviewBox.jsx":
/*!*******************************************!*\
  !*** ./app/components/CallPreviewBox.jsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CallPreviewBox = ()=>{\n    var _this = undefined;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [typingText, setTypingText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [typingComplete, setTypingComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [callHandled, setCallHandled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phoneNumber, setPhoneNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"(*************\");\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBrowser, setIsBrowser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Updated message flow to match user specification & placeholder for dynamic phone number\n    const callSteps = [\n        \"☎️ Missed Call {phoneNumber} — 1 minute ago\",\n        \"\\uD83D\\uDD14 (Potential Customer) \\uD83D\\uDD14\",\n        \"⚡ CallSaver AI Assistant Activated via Call Forwarding...\",\n        \"\\uD83E\\uDD16 AI:\",\n        \"\\\"Hi! We noticed you just called. The person you're trying to reach is currently unavailable.\\n\\nI'm their AI assistant — how can I help you today?\\nWould you like to schedule an appointment or ask a quick question?\\\"\",\n        \"\\uD83E\\uDDD1 Customer:\",\n        '\"Hey, this is John. I wanted to check if Mr. XXX is available tomorrow at 8 PM.\\nCan you book that appointment for me?\"',\n        \"\\uD83E\\uDD16 AI:\",\n        \"✅ Done!\" // Step 8 (AI msg/status)\n    ];\n    // Check if we're in browser environment\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsBrowser(\"object\" !== \"undefined\");\n        setIsMounted(true);\n        return ()=>setIsMounted(false);\n    }, []);\n    // Rotate demo phone numbers only after mount and in browser\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted || !isBrowser) return;\n        const phoneNumbers = [\n            \"(*************\",\n            \"(*************\",\n            \"(*************\"\n        ];\n        let currentIndex = 0;\n        const interval = setInterval(()=>{\n            currentIndex = (currentIndex + 1) % phoneNumbers.length;\n            setPhoneNumber(phoneNumbers[currentIndex]);\n        }, 7000); // Change every 7 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        isMounted,\n        isBrowser\n    ]);\n    // Initialize with the first step text, dynamically inserting phone number - Runs only once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted || !isBrowser) return;\n        // Set initial text only once\n        const firstStepText = callSteps[0].replace(\"{phoneNumber}\", phoneNumber);\n        setTypingText(firstStepText);\n        const timer = setTimeout(()=>{\n            setTypingComplete(true);\n        }, 500);\n        return ()=>clearTimeout(timer);\n    }, [\n        isMounted,\n        isBrowser\n    ]); // Removed phoneNumber dependency to prevent re-init on number change\n    // Function to get the correct step text with dynamic phone number\n    const getStepText = (index)=>{\n        if (index === 0) {\n            return callSteps[0].replace(\"{phoneNumber}\", phoneNumber);\n        }\n        return callSteps[index];\n    };\n    // Handle progression through steps\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted || !isBrowser) return;\n        // Use getStepText to ensure dynamic phone number is used if needed\n        if (typingComplete && currentStep < callSteps.length - 1) {\n            const nextStepTimer = setTimeout(()=>{\n                var _getStepText;\n                const nextStepIndex = currentStep + 1;\n                setCurrentStep(nextStepIndex);\n                // Ensure we get the correct text, especially for step 0 if logic revisits it\n                setTypingText(getStepText(nextStepIndex));\n                setTypingComplete(false);\n                // Mark typing complete after a delay to simulate typing\n                // User messages (step 6) should have a more realistic typing effect\n                const isUserMessage = nextStepIndex === 6; // Index 6 is Customer message\n                const typingDelay = isUserMessage ? 2000 // Longer for user message to show typing effect\n                 : (((_getStepText = getStepText(nextStepIndex)) === null || _getStepText === void 0 ? void 0 : _getStepText.length) || 0) * 15; // Use content length\n                const minDelay = isUserMessage ? 2000 : 800;\n                const typingCompleteTimer = setTimeout(()=>{\n                    setTypingComplete(true);\n                }, Math.max(typingDelay, minDelay));\n                return ()=>clearTimeout(typingCompleteTimer);\n            }, 1200); // Slight pause between messages\n            return ()=>clearTimeout(nextStepTimer);\n        } else if (typingComplete && currentStep === callSteps.length - 1 && !callHandled) {\n            // Show call handled success badge after the last step\n            const successTimer = setTimeout(()=>{\n                setCallHandled(true);\n            }, 1500);\n            return ()=>clearTimeout(successTimer);\n        }\n    }, [\n        currentStep,\n        typingComplete,\n        callHandled,\n        callSteps,\n        isMounted,\n        isBrowser\n    ]);\n    // If not mounted or not in browser, return a placeholder\n    if (!isMounted || !isBrowser) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border-2 border-purple-500/50 bg-gray-900/80 rounded-2xl overflow-hidden shadow-[0_0_40px_rgba(139,92,246,0.6)] backdrop-blur-sm w-full max-w-sm mx-auto sm:max-w-md lg:max-w-[420px] aspect-[9/19] sm:aspect-[9/16]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 h-12 rounded-full border-t-2 border-b-2 border-purple-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Pulsing emoji animation for special characters\n    const pulseEmoji = function(text) {\n        let isPrimary = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // For emoji at the start of the text\n        if (text.startsWith(\"☎️\") || text.startsWith(\"⚡\") || text.startsWith(\"\\uD83D\\uDFE3\") || text.startsWith(\"\\uD83E\\uDD16\") || text.startsWith(\"\\uD83E\\uDDD1\") || text.startsWith(\"\\uD83D\\uDD14\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 1.5,\n                            repeat: Infinity,\n                            repeatType: \"loop\"\n                        },\n                        className: \"inline-block mr-1\",\n                        children: text.substring(0, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, _this),\n                    text.substring(2)\n                ]\n            }, void 0, true);\n        }\n        // Handle ending bell emoji for \"Potential Customer\"\n        if (text.includes(\"(Potential Customer) \\uD83D\\uDD14\")) {\n            const beforeBell = text.substring(0, text.indexOf(\"\\uD83D\\uDD14\"));\n            const bell = \"\\uD83D\\uDD14\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    beforeBell,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 1.5,\n                            repeat: Infinity,\n                            repeatType: \"loop\"\n                        },\n                        className: \"inline-block ml-1\" // Adjust spacing if needed\n                        ,\n                        children: bell\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true);\n        }\n        // Special handling for \"✅ Done!\" with bounce animation\n        if (text === \"✅ Done!\" && isPrimary) {\n            const checkAndDone = \"✅ Done!\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                    initial: {\n                        scale: 0.8\n                    },\n                    animate: {\n                        scale: [\n                            0.8,\n                            1.2,\n                            1\n                        ]\n                    },\n                    transition: {\n                        duration: 0.6,\n                        ease: \"easeOut\"\n                    },\n                    className: \"inline-block text-green-400 font-bold\",\n                    children: checkAndDone\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false);\n        }\n        return text;\n    };\n    // Function to format text with line breaks\n    const formatText = (text)=>{\n        if (!text) return null;\n        // Ensure text is a string before splitting\n        if (typeof text !== \"string\") {\n            return text; // Return as is if not a string\n        }\n        // Replace \\n with line breaks for proper display\n        return text.split(\"\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block\",\n                children: [\n                    i > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 196,\n                        columnNumber: 19\n                    }, undefined),\n                    line\n                ]\n            }, i, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined));\n    };\n    // Base container animation\n    const containerVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    // Message animation variants\n    const messageVariants = {\n        hidden: {\n            opacity: 0,\n            y: 10\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.4,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    // Function to determine message bubble styles based on sender - Adjusted indices\n    const getMessageStyles = (index)=>{\n        // AI message styles (indices 3, 4, 7, 8)\n        if (index === 3 || index === 4 || index === 7 || index === 8) {\n            return \"bg-purple-900/40 border border-purple-500/30 backdrop-blur-sm shadow-sm ml-4\";\n        } else if (index === 5 || index === 6) {\n            return \"bg-blue-900/40 border border-blue-500/30 backdrop-blur-sm shadow-sm ml-0 mr-4\";\n        }\n        // System message styles (indices 0-2)\n        return \"bg-transparent\";\n    };\n    // Determine if message is primary content (for special formatting) - Adjusted indices\n    const isMessagePrimary = (index)=>{\n        // Indices of main message content: AI msg (4), Customer msg (6), Done! (8)\n        return index === 4 || index === 6 || index === 8;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: \"hidden\",\n        animate: \"visible\",\n        variants: containerVariants,\n        ref: componentRef,\n        className: \"relative border-2 border-purple-500/50 bg-gray-900/80 rounded-2xl overflow-hidden shadow-[0_0_40px_rgba(139,92,246,0.6)] backdrop-blur-sm w-full max-w-sm mx-auto sm:max-w-md lg:max-w-[420px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 -z-10 rounded-2xl\",\n                animate: {\n                    boxShadow: [\n                        \"0 0 20px rgba(139, 92, 246, 0.3)\",\n                        \"0 0 30px rgba(139, 92, 246, 0.6)\",\n                        \"0 0 20px rgba(139, 92, 246, 0.3)\"\n                    ]\n                },\n                transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"aspect-[9/19] sm:aspect-[9/16] w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10 rounded-3xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-2xl transform scale-125 animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-10 -right-10 w-32 h-32 rounded-full bg-blue-500/10 blur-xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-purple-500/10 blur-xl animate-pulse-slow\",\n                        style: {\n                            animationDelay: \"1s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 p-4 sm:p-6 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4 pb-3 border-b border-purple-500/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-base font-semibold text-white\",\n                                        children: \"Callsaver AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                className: \"w-2.5 h-2.5 rounded-full bg-green-400 mr-1.5\",\n                                                animate: {\n                                                    boxShadow: [\n                                                        \"0 0 0px rgba(74, 222, 128, 0)\",\n                                                        \"0 0 8px rgba(74, 222, 128, 0.7)\",\n                                                        \"0 0 0px rgba(74, 222, 128, 0)\"\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium text-green-400\",\n                                                children: \"ACTIVE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-purple-300 mb-3 text-center font-medium bg-purple-900/20 rounded-full py-1 px-3 backdrop-blur-sm\",\n                                children: \"Incoming Call Simulation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden bg-gradient-to-br from-purple-800/30 via-blue-800/20 to-transparent backdrop-blur-md rounded-2xl border border-purple-500/30 p-4 sm:p-5 mb-4 relative shadow-inner\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto space-y-3 text-sm sm:text-base\",\n                                        children: [\n                                            Array.from({\n                                                length: currentStep\n                                            }).map((_, index)=>{\n                                                const stepText = getStepText(index); // Use helper function\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    initial: \"hidden\",\n                                                    animate: \"visible\",\n                                                    variants: messageVariants,\n                                                    className: \"text-white p-2 rounded \".concat(getMessageStyles(index)),\n                                                    children: [\n                                                        isMessagePrimary(index) ? formatText(pulseEmoji(stepText, true)) // Use stepText\n                                                         : pulseEmoji(stepText),\n                                                        \" \"\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined);\n                                            }),\n                                            currentStep < callSteps.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                initial: \"hidden\",\n                                                animate: \"visible\",\n                                                variants: messageVariants,\n                                                className: \"text-white font-medium p-2 rounded \".concat(getMessageStyles(currentStep)),\n                                                children: [\n                                                    currentStep === 6 && !typingComplete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83E\\uDDD1 Customer:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-2 flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                        animate: {\n                                                                            y: [\n                                                                                0,\n                                                                                -3,\n                                                                                0\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.6,\n                                                                            repeat: Infinity,\n                                                                            repeatType: \"loop\",\n                                                                            delay: 0\n                                                                        },\n                                                                        className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                        animate: {\n                                                                            y: [\n                                                                                0,\n                                                                                -3,\n                                                                                0\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.6,\n                                                                            repeat: Infinity,\n                                                                            repeatType: \"loop\",\n                                                                            delay: 0.2\n                                                                        },\n                                                                        className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                        animate: {\n                                                                            y: [\n                                                                                0,\n                                                                                -3,\n                                                                                0\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.6,\n                                                                            repeat: Infinity,\n                                                                            repeatType: \"loop\",\n                                                                            delay: 0.4\n                                                                        },\n                                                                        className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, undefined) : isMessagePrimary(currentStep) ? formatText(pulseEmoji(typingText, true)) : pulseEmoji(typingText),\n                                                    !typingComplete && currentStep !== 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                                        animate: {\n                                                            opacity: [\n                                                                1,\n                                                                0\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            repeat: Infinity,\n                                                            repeatType: \"reverse\"\n                                                        },\n                                                        className: \"inline-block ml-1 w-2 h-4 bg-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    callHandled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            scale: 0.8,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        className: \"bg-green-600/30 border border-green-500/40 rounded-xl px-3 py-2 flex flex-col items-center justify-center shadow-md shadow-green-900/20 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        initial: {\n                                                            scale: 0.5\n                                                        },\n                                                        animate: {\n                                                            scale: [\n                                                                0.5,\n                                                                1.2,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5\n                                                        },\n                                                        className: \"w-5 h-5 bg-green-500 rounded-full mr-2 flex items-center justify-center text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-3 w-3\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-400\",\n                                                        children: \"Call Handled Successfully\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    delay: 0.5\n                                                },\n                                                className: \"flex items-center mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"No Missed Opportunities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                                        animate: {\n                                                            rotate: [\n                                                                0,\n                                                                15,\n                                                                -15,\n                                                                0\n                                                            ],\n                                                            scale: [\n                                                                1,\n                                                                1.1,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1.5,\n                                                            repeat: Infinity,\n                                                            repeatType: \"loop\",\n                                                            delay: 1\n                                                        },\n                                                        className: \"ml-1\",\n                                                        children: \"\\uD83D\\uDE80\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !callHandled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 flex items-center justify-center relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-full border-2 border-purple-500/50\",\n                                                    animate: {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ],\n                                                        opacity: [\n                                                            0.7,\n                                                            0,\n                                                            0.7\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-full border-2 border-purple-500/50\",\n                                                    animate: {\n                                                        scale: [\n                                                            1,\n                                                            1.5,\n                                                            1\n                                                        ],\n                                                        opacity: [\n                                                            0.5,\n                                                            0,\n                                                            0.5\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\",\n                                                        delay: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"absolute inset-2 rounded-full border-2 border-purple-500/80\",\n                                                    animate: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 8,\n                                                        repeat: Infinity,\n                                                        ease: \"linear\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        className: \"absolute w-2.5 h-2.5 rounded-full bg-purple-500 shadow-sm shadow-purple-900/50 top-0 left-1/2 -translate-x-1/2 -translate-y-1/2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"w-10 h-10 rounded-full bg-purple-600/40 flex items-center justify-center shadow-inner\",\n                                                    animate: {\n                                                        boxShadow: [\n                                                            \"0 0 0px rgba(147, 51, 234, 0)\",\n                                                            \"0 0 15px rgba(147, 51, 234, 0.5)\",\n                                                            \"0 0 0px rgba(147, 51, 234, 0)\"\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 2,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6 text-purple-300\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.07 0m-9.9-2.83a9 9 0 0112.73 0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CallPreviewBox, \"DS1hGH8uEeyS4UNEBc/8ix7T9WE=\");\n_c = CallPreviewBox;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CallPreviewBox);\nvar _c;\n$RefreshReg$(_c, \"CallPreviewBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CallPreviewBox.jsx\n"));

/***/ })

}]);