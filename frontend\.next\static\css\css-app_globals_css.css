/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.container {
  width: 100%;
}
@media (min-width: 475px) {

  .container {
    max-width: 475px;
  }
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-2 {
  inset: 0.5rem;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-10 {
  bottom: -2.5rem;
}
.-bottom-20 {
  bottom: -5rem;
}
.-bottom-40 {
  bottom: -10rem;
}
.-bottom-60 {
  bottom: -15rem;
}
.-left-10 {
  left: -2.5rem;
}
.-left-20 {
  left: -5rem;
}
.-left-40 {
  left: -10rem;
}
.-left-60 {
  left: -15rem;
}
.-right-1 {
  right: -0.25rem;
}
.-right-10 {
  right: -2.5rem;
}
.-right-2 {
  right: -0.5rem;
}
.-right-20 {
  right: -5rem;
}
.-right-40 {
  right: -10rem;
}
.-right-60 {
  right: -15rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-10 {
  top: -2.5rem;
}
.-top-16 {
  top: -4rem;
}
.-top-2 {
  top: -0.5rem;
}
.-top-20 {
  top: -5rem;
}
.-top-40 {
  top: -10rem;
}
.-top-60 {
  top: -15rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/3 {
  left: 33.333333%;
}
.left-1\/4 {
  left: 25%;
}
.left-2 {
  left: 0.5rem;
}
.left-20 {
  left: 5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-6 {
  left: 1.5rem;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-1\/3 {
  right: 33.333333%;
}
.right-1\/4 {
  right: 25%;
}
.right-2 {
  right: 0.5rem;
}
.right-20 {
  right: 5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-8 {
  right: 2rem;
}
.right-full {
  right: 100%;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-1\/3 {
  top: 33.333333%;
}
.top-16 {
  top: 4rem;
}
.top-2 {
  top: 0.5rem;
}
.top-2\.5 {
  top: 0.625rem;
}
.top-24 {
  top: 6rem;
}
.top-3 {
  top: 0.75rem;
}
.top-3\.5 {
  top: 0.875rem;
}
.top-4 {
  top: 1rem;
}
.top-6 {
  top: 1.5rem;
}
.top-full {
  top: 100%;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[-5\] {
  z-index: -5;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.m-4 {
  margin: 1rem;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-mx-1\.5 {
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.-mx-5 {
  margin-left: -1.25rem;
  margin-right: -1.25rem;
}
.-my-1\.5 {
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.-mb-px {
  margin-bottom: -1px;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-14 {
  margin-bottom: 3.5rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-0 {
  margin-left: 0px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-2\.5 {
  margin-left: 0.625rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-64 {
  margin-left: 16rem;
}
.ml-8 {
  margin-left: 2rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-0\.5 {
  margin-right: 0.125rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-2\.5 {
  margin-right: 0.625rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-6 {
  margin-right: 1.5rem;
}
.mr-auto {
  margin-right: auto;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.\!inline {
  display: inline !important;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-\[9\/19\] {
  aspect-ratio: 9/19;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-1\/2 {
  height: 50%;
}
.h-1\/3 {
  height: 33.333333%;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-2\/3 {
  height: 66.666667%;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-36 {
  height: 9rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-52 {
  height: 13rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[180px\] {
  height: 180px;
}
.h-\[20rem\] {
  height: 20rem;
}
.h-\[25rem\] {
  height: 25rem;
}
.h-\[30px\] {
  height: 30px;
}
.h-\[30rem\] {
  height: 30rem;
}
.h-\[35rem\] {
  height: 35rem;
}
.h-\[400px\] {
  height: 400px;
}
.h-\[40rem\] {
  height: 40rem;
}
.h-\[481px\] {
  height: 481px;
}
.h-\[495px\] {
  height: 495px;
}
.h-\[50rem\] {
  height: 50rem;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[650px\] {
  height: 650px;
}
.h-\[700px\] {
  height: 700px;
}
.h-\[70vh\] {
  height: 70vh;
}
.h-\[800px\] {
  height: 800px;
}
.h-\[80px\] {
  height: 80px;
}
.h-\[calc\(100\%-120px\)\] {
  height: calc(100% - 120px);
}
.h-\[calc\(100\%-160px\)\] {
  height: calc(100% - 160px);
}
.h-\[calc\(100\%-60px\)\] {
  height: calc(100% - 60px);
}
.h-\[calc\(100\%-80px\)\] {
  height: calc(100% - 80px);
}
.h-\[calc\(100vh-12rem\)\] {
  height: calc(100vh - 12rem);
}
.h-\[calc\(100vh-200px\)\] {
  height: calc(100vh - 200px);
}
.h-\[calc\(100vh-250px\)\] {
  height: calc(100vh - 250px);
}
.h-\[calc\(100vh-270px\)\] {
  height: calc(100vh - 270px);
}
.h-\[calc\(100vh-280px\)\] {
  height: calc(100vh - 280px);
}
.h-\[calc\(100vh-2rem\)\] {
  height: calc(100vh - 2rem);
}
.h-\[calc\(100vh-400px\)\] {
  height: calc(100vh - 400px);
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-24 {
  max-height: 6rem;
}
.max-h-40 {
  max-height: 10rem;
}
.max-h-60 {
  max-height: 15rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-\[481px\] {
  max-height: 481px;
}
.max-h-\[500px\] {
  max-height: 500px;
}
.max-h-\[600px\] {
  max-height: 600px;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.min-h-\[100px\] {
  min-height: 100px;
}
.min-h-\[150px\] {
  min-height: 150px;
}
.min-h-\[220px\] {
  min-height: 220px;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[320px\] {
  min-height: 320px;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[50px\] {
  min-height: 50px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-full {
  min-height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0\.5 {
  width: 0.125rem;
}
.w-1 {
  width: 0.25rem;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-1\/4 {
  width: 25%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-5\/6 {
  width: 83.333333%;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[130px\] {
  width: 130px;
}
.w-\[180px\] {
  width: 180px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[20rem\] {
  width: 20rem;
}
.w-\[25rem\] {
  width: 25rem;
}
.w-\[30rem\] {
  width: 30rem;
}
.w-\[320px\] {
  width: 320px;
}
.w-\[35rem\] {
  width: 35rem;
}
.w-\[40rem\] {
  width: 40rem;
}
.w-\[50rem\] {
  width: 50rem;
}
.w-\[calc\(100\%-1rem\)\] {
  width: calc(100% - 1rem);
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.w-screen {
  width: 100vw;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[100px\] {
  min-width: 100px;
}
.min-w-\[180px\] {
  min-width: 180px;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[220px\] {
  min-width: 220px;
}
.min-w-\[300px\] {
  min-width: 300px;
}
.min-w-\[40px\] {
  min-width: 40px;
}
.min-w-\[60px\] {
  min-width: 60px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.min-w-full {
  min-width: 100%;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[1200px\] {
  max-width: 1200px;
}
.max-w-\[200px\] {
  max-width: 200px;
}
.max-w-\[70\%\] {
  max-width: 70%;
}
.max-w-\[75\%\] {
  max-width: 75%;
}
.max-w-\[80\%\] {
  max-width: 80%;
}
.max-w-\[80px\] {
  max-width: 80px;
}
.max-w-\[85\%\] {
  max-width: 85%;
}
.max-w-\[95\%\] {
  max-width: 95%;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-top-right {
  transform-origin: top right;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-6 {
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[30\%\] {
  --tw-translate-x: 30%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[40\%\] {
  --tw-translate-y: 40%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-150 {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes fadeIn {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse-slow {
  animation: pulse 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes slideIn {

  0% {
    transform: translateY(-10px);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-grab {
  cursor: grab;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-wait {
  cursor: wait;
}
.touch-auto {
  touch-action: auto;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-3\.5 {
  gap: 0.875rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-8 {
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}
.gap-y-3 {
  row-gap: 0.75rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.gap-y-8 {
  row-gap: 2rem;
}
.-space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(0.375rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.divide-gray-800 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-divide-opacity, 1));
}
.divide-gray-800\/60 > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(31 41 55 / 0.6);
}
.self-start {
  align-self: flex-start;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.scroll-smooth {
  scroll-behavior: smooth;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[16px\] {
  border-radius: 16px;
}
.rounded-\[40px\] {
  border-radius: 40px;
}
.rounded-\[55px\] {
  border-radius: 55px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-l-xl {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.rounded-r-xl {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.rounded-t-sm {
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
}
.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-bl-\[4px\] {
  border-bottom-left-radius: 4px;
}
.rounded-bl-lg {
  border-bottom-left-radius: 0.5rem;
}
.rounded-bl-xl {
  border-bottom-left-radius: 0.75rem;
}
.rounded-br-\[4px\] {
  border-bottom-right-radius: 4px;
}
.rounded-br-lg {
  border-bottom-right-radius: 0.5rem;
}
.rounded-br-xl {
  border-bottom-right-radius: 0.75rem;
}
.rounded-tl-lg {
  border-top-left-radius: 0.5rem;
}
.rounded-tr-lg {
  border-top-right-radius: 0.5rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border-\[14px\] {
  border-width: 14px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-0 {
  border-left-width: 0px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-dashed {
  border-style: dashed;
}
.border-amber-500 {
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-500\/20 {
  border-color: rgb(59 130 246 / 0.2);
}
.border-blue-500\/30 {
  border-color: rgb(59 130 246 / 0.3);
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-blue-600\/40 {
  border-color: rgb(37 99 235 / 0.4);
}
.border-blue-800\/30 {
  border-color: rgb(30 64 175 / 0.3);
}
.border-blue-800\/50 {
  border-color: rgb(30 64 175 / 0.5);
}
.border-fuchsia-500\/20 {
  border-color: rgb(217 70 239 / 0.2);
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.border-gray-500\/30 {
  border-color: rgb(107 114 128 / 0.3);
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.border-gray-600\/30 {
  border-color: rgb(75 85 99 / 0.3);
}
.border-gray-600\/50 {
  border-color: rgb(75 85 99 / 0.5);
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-gray-700\/20 {
  border-color: rgb(55 65 81 / 0.2);
}
.border-gray-700\/30 {
  border-color: rgb(55 65 81 / 0.3);
}
.border-gray-700\/40 {
  border-color: rgb(55 65 81 / 0.4);
}
.border-gray-700\/50 {
  border-color: rgb(55 65 81 / 0.5);
}
.border-gray-750 {
  --tw-border-opacity: 1;
  border-color: rgb(45 55 72 / var(--tw-border-opacity, 1));
}
.border-gray-750\/50 {
  border-color: rgb(45 55 72 / 0.5);
}
.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.border-gray-800\/30 {
  border-color: rgb(31 41 55 / 0.3);
}
.border-gray-800\/50 {
  border-color: rgb(31 41 55 / 0.5);
}
.border-gray-800\/60 {
  border-color: rgb(31 41 55 / 0.6);
}
.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-green-400\/30 {
  border-color: rgb(74 222 128 / 0.3);
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-green-500\/20 {
  border-color: rgb(34 197 94 / 0.2);
}
.border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}
.border-green-500\/40 {
  border-color: rgb(34 197 94 / 0.4);
}
.border-green-600\/30 {
  border-color: rgb(22 163 74 / 0.3);
}
.border-green-600\/40 {
  border-color: rgb(22 163 74 / 0.4);
}
.border-green-700 {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.border-green-700\/30 {
  border-color: rgb(21 128 61 / 0.3);
}
.border-green-800\/30 {
  border-color: rgb(22 101 52 / 0.3);
}
.border-green-800\/50 {
  border-color: rgb(22 101 52 / 0.5);
}
.border-indigo-100 {
  --tw-border-opacity: 1;
  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));
}
.border-indigo-200 {
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}
.border-indigo-400 {
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.border-indigo-400\/20 {
  border-color: rgb(129 140 248 / 0.2);
}
.border-indigo-400\/30 {
  border-color: rgb(129 140 248 / 0.3);
}
.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.border-indigo-500\/20 {
  border-color: rgb(99 102 241 / 0.2);
}
.border-indigo-500\/30 {
  border-color: rgb(99 102 241 / 0.3);
}
.border-indigo-600 {
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}
.border-indigo-600\/30 {
  border-color: rgb(79 70 229 / 0.3);
}
.border-indigo-600\/40 {
  border-color: rgb(79 70 229 / 0.4);
}
.border-indigo-700\/20 {
  border-color: rgb(67 56 202 / 0.2);
}
.border-indigo-700\/30 {
  border-color: rgb(67 56 202 / 0.3);
}
.border-indigo-800\/30 {
  border-color: rgb(55 48 163 / 0.3);
}
.border-indigo-800\/50 {
  border-color: rgb(55 48 163 / 0.5);
}
.border-pink-400\/30 {
  border-color: rgb(244 114 182 / 0.3);
}
.border-pink-500\/20 {
  border-color: rgb(236 72 153 / 0.2);
}
.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-purple-400 {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-purple-400\/30 {
  border-color: rgb(192 132 252 / 0.3);
}
.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-purple-500\/20 {
  border-color: rgb(168 85 247 / 0.2);
}
.border-purple-500\/30 {
  border-color: rgb(168 85 247 / 0.3);
}
.border-purple-500\/40 {
  border-color: rgb(168 85 247 / 0.4);
}
.border-purple-500\/50 {
  border-color: rgb(168 85 247 / 0.5);
}
.border-purple-500\/60 {
  border-color: rgb(168 85 247 / 0.6);
}
.border-purple-500\/80 {
  border-color: rgb(168 85 247 / 0.8);
}
.border-purple-600\/40 {
  border-color: rgb(147 51 234 / 0.4);
}
.border-purple-700 {
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}
.border-purple-700\/50 {
  border-color: rgb(126 34 206 / 0.5);
}
.border-purple-800\/20 {
  border-color: rgb(107 33 168 / 0.2);
}
.border-purple-800\/30 {
  border-color: rgb(107 33 168 / 0.3);
}
.border-purple-900\/20 {
  border-color: rgb(88 28 135 / 0.2);
}
.border-purple-900\/30 {
  border-color: rgb(88 28 135 / 0.3);
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-500\/20 {
  border-color: rgb(239 68 68 / 0.2);
}
.border-red-500\/30 {
  border-color: rgb(239 68 68 / 0.3);
}
.border-red-600\/40 {
  border-color: rgb(220 38 38 / 0.4);
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.border-red-800\/30 {
  border-color: rgb(153 27 27 / 0.3);
}
.border-red-800\/40 {
  border-color: rgb(153 27 27 / 0.4);
}
.border-red-800\/50 {
  border-color: rgb(153 27 27 / 0.5);
}
.border-transparent {
  border-color: transparent;
}
.border-violet-500\/20 {
  border-color: rgb(139 92 246 / 0.2);
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}
.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}
.border-yellow-500\/20 {
  border-color: rgb(234 179 8 / 0.2);
}
.border-yellow-500\/30 {
  border-color: rgb(234 179 8 / 0.3);
}
.border-yellow-600\/40 {
  border-color: rgb(202 138 4 / 0.4);
}
.border-yellow-700 {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.border-yellow-700\/30 {
  border-color: rgb(161 98 7 / 0.3);
}
.border-yellow-800\/30 {
  border-color: rgb(133 77 14 / 0.3);
}
.border-yellow-800\/50 {
  border-color: rgb(133 77 14 / 0.5);
}
.border-l-indigo-500 {
  --tw-border-opacity: 1;
  border-left-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.border-opacity-30 {
  --tw-border-opacity: 0.3;
}
.bg-\[\#0a0a14\] {
  --tw-bg-opacity: 1;
  background-color: rgb(10 10 20 / var(--tw-bg-opacity, 1));
}
.bg-\[\#0d0d17\] {
  --tw-bg-opacity: 1;
  background-color: rgb(13 13 23 / var(--tw-bg-opacity, 1));
}
.bg-\[\#111125\] {
  --tw-bg-opacity: 1;
  background-color: rgb(17 17 37 / var(--tw-bg-opacity, 1));
}
.bg-\[\#13132a\]\/30 {
  background-color: rgb(19 19 42 / 0.3);
}
.bg-\[\#1982FC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(25 130 252 / var(--tw-bg-opacity, 1));
}
.bg-\[\#1a1a35\] {
  --tw-bg-opacity: 1;
  background-color: rgb(26 26 53 / var(--tw-bg-opacity, 1));
}
.bg-\[\#1a1a35\]\/50 {
  background-color: rgb(26 26 53 / 0.5);
}
.bg-\[\#28293d\] {
  --tw-bg-opacity: 1;
  background-color: rgb(40 41 61 / var(--tw-bg-opacity, 1));
}
.bg-\[\#2c2c44\] {
  --tw-bg-opacity: 1;
  background-color: rgb(44 44 68 / var(--tw-bg-opacity, 1));
}
.bg-\[\#331418\] {
  --tw-bg-opacity: 1;
  background-color: rgb(51 20 24 / var(--tw-bg-opacity, 1));
}
.bg-\[\#3b3363\] {
  --tw-bg-opacity: 1;
  background-color: rgb(59 51 99 / var(--tw-bg-opacity, 1));
}
.bg-\[\#6946db\] {
  --tw-bg-opacity: 1;
  background-color: rgb(105 70 219 / var(--tw-bg-opacity, 1));
}
.bg-\[\#E9E9EB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(233 233 235 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f1f1f1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 241 241 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f8f8f8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(248 248 248 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f9f9f9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 249 249 / var(--tw-bg-opacity, 1));
}
.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.bg-amber-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}
.bg-background {
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/70 {
  background-color: rgb(0 0 0 / 0.7);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-400\/10 {
  background-color: rgb(96 165 250 / 0.1);
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}
.bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}
.bg-blue-500\/5 {
  background-color: rgb(59 130 246 / 0.05);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-blue-600\/20 {
  background-color: rgb(37 99 235 / 0.2);
}
.bg-blue-600\/30 {
  background-color: rgb(37 99 235 / 0.3);
}
.bg-blue-600\/5 {
  background-color: rgb(37 99 235 / 0.05);
}
.bg-blue-600\/50 {
  background-color: rgb(37 99 235 / 0.5);
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.bg-blue-700\/60 {
  background-color: rgb(29 78 216 / 0.6);
}
.bg-blue-700\/80 {
  background-color: rgb(29 78 216 / 0.8);
}
.bg-blue-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.bg-blue-900\/10 {
  background-color: rgb(30 58 138 / 0.1);
}
.bg-blue-900\/20 {
  background-color: rgb(30 58 138 / 0.2);
}
.bg-blue-900\/30 {
  background-color: rgb(30 58 138 / 0.3);
}
.bg-blue-900\/40 {
  background-color: rgb(30 58 138 / 0.4);
}
.bg-blue-900\/50 {
  background-color: rgb(30 58 138 / 0.5);
}
.bg-blue-950\/50 {
  background-color: rgb(23 37 84 / 0.5);
}
.bg-fuchsia-500\/10 {
  background-color: rgb(217 70 239 / 0.1);
}
.bg-fuchsia-500\/20 {
  background-color: rgb(217 70 239 / 0.2);
}
.bg-fuchsia-600\/5 {
  background-color: rgb(192 38 211 / 0.05);
}
.bg-gradient-dark {
  background-color: linear-gradient(180deg, #1a202c 0%, #0f172a 100%);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-200\/50 {
  background-color: rgb(229 231 235 / 0.5);
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-400\/10 {
  background-color: rgb(156 163 175 / 0.1);
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/20 {
  background-color: rgb(107 114 128 / 0.2);
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-600\/50 {
  background-color: rgb(75 85 99 / 0.5);
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-700\/10 {
  background-color: rgb(55 65 81 / 0.1);
}
.bg-gray-700\/30 {
  background-color: rgb(55 65 81 / 0.3);
}
.bg-gray-700\/50 {
  background-color: rgb(55 65 81 / 0.5);
}
.bg-gray-700\/70 {
  background-color: rgb(55 65 81 / 0.7);
}
.bg-gray-750 {
  --tw-bg-opacity: 1;
  background-color: rgb(45 55 72 / var(--tw-bg-opacity, 1));
}
.bg-gray-750\/50 {
  background-color: rgb(45 55 72 / 0.5);
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-800\/20 {
  background-color: rgb(31 41 55 / 0.2);
}
.bg-gray-800\/30 {
  background-color: rgb(31 41 55 / 0.3);
}
.bg-gray-800\/40 {
  background-color: rgb(31 41 55 / 0.4);
}
.bg-gray-800\/50 {
  background-color: rgb(31 41 55 / 0.5);
}
.bg-gray-800\/60 {
  background-color: rgb(31 41 55 / 0.6);
}
.bg-gray-800\/70 {
  background-color: rgb(31 41 55 / 0.7);
}
.bg-gray-800\/80 {
  background-color: rgb(31 41 55 / 0.8);
}
.bg-gray-850 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 32 44 / var(--tw-bg-opacity, 1));
}
.bg-gray-850\/30 {
  background-color: rgb(26 32 44 / 0.3);
}
.bg-gray-850\/50 {
  background-color: rgb(26 32 44 / 0.5);
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-gray-900\/30 {
  background-color: rgb(17 24 39 / 0.3);
}
.bg-gray-900\/50 {
  background-color: rgb(17 24 39 / 0.5);
}
.bg-gray-900\/60 {
  background-color: rgb(17 24 39 / 0.6);
}
.bg-gray-900\/70 {
  background-color: rgb(17 24 39 / 0.7);
}
.bg-gray-900\/80 {
  background-color: rgb(17 24 39 / 0.8);
}
.bg-gray-900\/90 {
  background-color: rgb(17 24 39 / 0.9);
}
.bg-gray-900\/95 {
  background-color: rgb(17 24 39 / 0.95);
}
.bg-gray-950 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-400\/10 {
  background-color: rgb(74 222 128 / 0.1);
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/10 {
  background-color: rgb(34 197 94 / 0.1);
}
.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}
.bg-green-500\/30 {
  background-color: rgb(34 197 94 / 0.3);
}
.bg-green-500\/80 {
  background-color: rgb(34 197 94 / 0.8);
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-600\/20 {
  background-color: rgb(22 163 74 / 0.2);
}
.bg-green-600\/30 {
  background-color: rgb(22 163 74 / 0.3);
}
.bg-green-600\/50 {
  background-color: rgb(22 163 74 / 0.5);
}
.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.bg-green-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}
.bg-green-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.bg-green-900\/10 {
  background-color: rgb(20 83 45 / 0.1);
}
.bg-green-900\/20 {
  background-color: rgb(20 83 45 / 0.2);
}
.bg-green-900\/30 {
  background-color: rgb(20 83 45 / 0.3);
}
.bg-green-900\/40 {
  background-color: rgb(20 83 45 / 0.4);
}
.bg-green-900\/50 {
  background-color: rgb(20 83 45 / 0.5);
}
.bg-green-950\/50 {
  background-color: rgb(5 46 22 / 0.5);
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(129 140 248 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500\/10 {
  background-color: rgb(99 102 241 / 0.1);
}
.bg-indigo-500\/20 {
  background-color: rgb(99 102 241 / 0.2);
}
.bg-indigo-500\/80 {
  background-color: rgb(99 102 241 / 0.8);
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-indigo-600\/20 {
  background-color: rgb(79 70 229 / 0.2);
}
.bg-indigo-600\/30 {
  background-color: rgb(79 70 229 / 0.3);
}
.bg-indigo-600\/5 {
  background-color: rgb(79 70 229 / 0.05);
}
.bg-indigo-600\/50 {
  background-color: rgb(79 70 229 / 0.5);
}
.bg-indigo-600\/60 {
  background-color: rgb(79 70 229 / 0.6);
}
.bg-indigo-600\/70 {
  background-color: rgb(79 70 229 / 0.7);
}
.bg-indigo-600\/80 {
  background-color: rgb(79 70 229 / 0.8);
}
.bg-indigo-600\/90 {
  background-color: rgb(79 70 229 / 0.9);
}
.bg-indigo-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}
.bg-indigo-900\/20 {
  background-color: rgb(49 46 129 / 0.2);
}
.bg-indigo-900\/30 {
  background-color: rgb(49 46 129 / 0.3);
}
.bg-indigo-900\/40 {
  background-color: rgb(49 46 129 / 0.4);
}
.bg-indigo-900\/50 {
  background-color: rgb(49 46 129 / 0.5);
}
.bg-indigo-900\/60 {
  background-color: rgb(49 46 129 / 0.6);
}
.bg-indigo-950\/20 {
  background-color: rgb(30 27 75 / 0.2);
}
.bg-indigo-950\/50 {
  background-color: rgb(30 27 75 / 0.5);
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-500\/20 {
  background-color: rgb(249 115 22 / 0.2);
}
.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.bg-pink-500\/10 {
  background-color: rgb(236 72 153 / 0.1);
}
.bg-pink-500\/15 {
  background-color: rgb(236 72 153 / 0.15);
}
.bg-pink-500\/20 {
  background-color: rgb(236 72 153 / 0.2);
}
.bg-pink-600\/10 {
  background-color: rgb(219 39 119 / 0.1);
}
.bg-pink-600\/5 {
  background-color: rgb(219 39 119 / 0.05);
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-500\/10 {
  background-color: rgb(168 85 247 / 0.1);
}
.bg-purple-500\/15 {
  background-color: rgb(168 85 247 / 0.15);
}
.bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}
.bg-purple-500\/30 {
  background-color: rgb(168 85 247 / 0.3);
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-purple-600\/10 {
  background-color: rgb(147 51 234 / 0.1);
}
.bg-purple-600\/20 {
  background-color: rgb(147 51 234 / 0.2);
}
.bg-purple-600\/30 {
  background-color: rgb(147 51 234 / 0.3);
}
.bg-purple-600\/40 {
  background-color: rgb(147 51 234 / 0.4);
}
.bg-purple-600\/5 {
  background-color: rgb(147 51 234 / 0.05);
}
.bg-purple-600\/50 {
  background-color: rgb(147 51 234 / 0.5);
}
.bg-purple-600\/70 {
  background-color: rgb(147 51 234 / 0.7);
}
.bg-purple-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.bg-purple-700\/30 {
  background-color: rgb(126 34 206 / 0.3);
}
.bg-purple-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}
.bg-purple-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.bg-purple-900\/10 {
  background-color: rgb(88 28 135 / 0.1);
}
.bg-purple-900\/20 {
  background-color: rgb(88 28 135 / 0.2);
}
.bg-purple-900\/30 {
  background-color: rgb(88 28 135 / 0.3);
}
.bg-purple-900\/40 {
  background-color: rgb(88 28 135 / 0.4);
}
.bg-purple-900\/50 {
  background-color: rgb(88 28 135 / 0.5);
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-400\/10 {
  background-color: rgb(248 113 113 / 0.1);
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/10 {
  background-color: rgb(239 68 68 / 0.1);
}
.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}
.bg-red-500\/30 {
  background-color: rgb(239 68 68 / 0.3);
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-600\/30 {
  background-color: rgb(220 38 38 / 0.3);
}
.bg-red-600\/5 {
  background-color: rgb(220 38 38 / 0.05);
}
.bg-red-600\/50 {
  background-color: rgb(220 38 38 / 0.5);
}
.bg-red-700\/60 {
  background-color: rgb(185 28 28 / 0.6);
}
.bg-red-700\/80 {
  background-color: rgb(185 28 28 / 0.8);
}
.bg-red-800\/30 {
  background-color: rgb(153 27 27 / 0.3);
}
.bg-red-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.bg-red-900\/20 {
  background-color: rgb(127 29 29 / 0.2);
}
.bg-red-900\/30 {
  background-color: rgb(127 29 29 / 0.3);
}
.bg-red-900\/40 {
  background-color: rgb(127 29 29 / 0.4);
}
.bg-red-950\/50 {
  background-color: rgb(69 10 10 / 0.5);
}
.bg-transparent {
  background-color: transparent;
}
.bg-violet-500\/10 {
  background-color: rgb(139 92 246 / 0.1);
}
.bg-violet-500\/20 {
  background-color: rgb(139 92 246 / 0.2);
}
.bg-violet-600\/5 {
  background-color: rgb(124 58 237 / 0.05);
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/40 {
  background-color: rgb(255 255 255 / 0.4);
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400\/10 {
  background-color: rgb(250 204 21 / 0.1);
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/10 {
  background-color: rgb(234 179 8 / 0.1);
}
.bg-yellow-500\/20 {
  background-color: rgb(234 179 8 / 0.2);
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-yellow-600\/20 {
  background-color: rgb(202 138 4 / 0.2);
}
.bg-yellow-600\/30 {
  background-color: rgb(202 138 4 / 0.3);
}
.bg-yellow-600\/80 {
  background-color: rgb(202 138 4 / 0.8);
}
.bg-yellow-700\/60 {
  background-color: rgb(161 98 7 / 0.6);
}
.bg-yellow-700\/80 {
  background-color: rgb(161 98 7 / 0.8);
}
.bg-yellow-900\/30 {
  background-color: rgb(113 63 18 / 0.3);
}
.bg-yellow-900\/40 {
  background-color: rgb(113 63 18 / 0.4);
}
.bg-yellow-950\/50 {
  background-color: rgb(66 32 6 / 0.5);
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-grid-pattern {
  background-image: linear-gradient(to right, #1e293b 1px, transparent 1px), linear-gradient(to bottom, #1e293b 1px, transparent 1px);
}
.from-\[\#0d0d17\] {
  --tw-gradient-from: #0d0d17 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(13 13 23 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#1a1a35\] {
  --tw-gradient-from: #1a1a35 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 26 53 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#1a1a35\]\/60 {
  --tw-gradient-from: rgb(26 26 53 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 26 53 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#28293d\] {
  --tw-gradient-from: #28293d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(40 41 61 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#2c2c44\] {
  --tw-gradient-from: #2c2c44 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(44 44 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#331418\] {
  --tw-gradient-from: #331418 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(51 20 24 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#3b3363\] {
  --tw-gradient-from: #3b3363 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 51 99 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#6946db\] {
  --tw-gradient-from: #6946db var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(105 70 219 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-400 {
  --tw-gradient-from: #fbbf24 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(251 191 36 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600\/80 {
  --tw-gradient-from: rgb(37 99 235 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-900\/20 {
  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-700 {
  --tw-gradient-from: #374151 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900 {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900\/75 {
  --tw-gradient-from: rgb(17 24 39 / 0.75) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900\/90 {
  --tw-gradient-from: rgb(17 24 39 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-400 {
  --tw-gradient-from: #4ade80 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(74 222 128 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/60 {
  --tw-gradient-from: rgb(34 197 94 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600\/50 {
  --tw-gradient-from: rgb(22 163 74 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-900\/20 {
  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-900\/30 {
  --tw-gradient-from: rgb(20 83 45 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-300 {
  --tw-gradient-from: #a5b4fc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(165 180 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-400 {
  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(129 140 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500 {
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500\/10 {
  --tw-gradient-from: rgb(99 102 241 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500\/60 {
  --tw-gradient-from: rgb(99 102 241 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600 {
  --tw-gradient-from: #4f46e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600\/5 {
  --tw-gradient-from: rgb(79 70 229 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600\/50 {
  --tw-gradient-from: rgb(79 70 229 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(79 70 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-900\/20 {
  --tw-gradient-from: rgb(49 46 129 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-900\/50 {
  --tw-gradient-from: rgb(49 46 129 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500 {
  --tw-gradient-from: #ec4899 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500\/60 {
  --tw-gradient-from: rgb(236 72 153 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-600 {
  --tw-gradient-from: #db2777 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-600\/15 {
  --tw-gradient-from: rgb(219 39 119 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-600\/50 {
  --tw-gradient-from: rgb(219 39 119 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 39 119 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-900\/20 {
  --tw-gradient-from: rgb(131 24 67 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(131 24 67 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-300 {
  --tw-gradient-from: #d8b4fe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(216 180 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-400 {
  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/15 {
  --tw-gradient-from: rgb(168 85 247 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/20 {
  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/60 {
  --tw-gradient-from: rgb(168 85 247 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600 {
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600\/15 {
  --tw-gradient-from: rgb(147 51 234 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600\/30 {
  --tw-gradient-from: rgb(147 51 234 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600\/50 {
  --tw-gradient-from: rgb(147 51 234 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600\/80 {
  --tw-gradient-from: rgb(147 51 234 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-700\/30 {
  --tw-gradient-from: rgb(126 34 206 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-800\/30 {
  --tw-gradient-from: rgb(107 33 168 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(107 33 168 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-900\/20 {
  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-900\/30 {
  --tw-gradient-from: rgb(127 29 29 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-500\/5 {
  --tw-gradient-from: rgb(234 179 8 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-900\/20 {
  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-blue-800\/20 {
  --tw-gradient-to: rgb(30 64 175 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 64 175 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-200 {
  --tw-gradient-to: rgb(233 213 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #e9d5ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-500 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #a855f7 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[\#14141f\] {
  --tw-gradient-to: #14141f var(--tw-gradient-to-position);
}
.to-\[\#1a1a35\]\/90 {
  --tw-gradient-to: rgb(26 26 53 / 0.9) var(--tw-gradient-to-position);
}
.to-\[\#221c38\] {
  --tw-gradient-to: #221c38 var(--tw-gradient-to-position);
}
.to-\[\#252538\] {
  --tw-gradient-to: #252538 var(--tw-gradient-to-position);
}
.to-\[\#252638\] {
  --tw-gradient-to: #252638 var(--tw-gradient-to-position);
}
.to-\[\#2c2452\] {
  --tw-gradient-to: #2c2452 var(--tw-gradient-to-position);
}
.to-\[\#3a1418\] {
  --tw-gradient-to: #3a1418 var(--tw-gradient-to-position);
}
.to-\[\#5935bf\] {
  --tw-gradient-to: #5935bf var(--tw-gradient-to-position);
}
.to-blue-400 {
  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);
}
.to-blue-500 {
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.to-blue-500\/60 {
  --tw-gradient-to: rgb(59 130 246 / 0.6) var(--tw-gradient-to-position);
}
.to-blue-500\/80 {
  --tw-gradient-to: rgb(59 130 246 / 0.8) var(--tw-gradient-to-position);
}
.to-blue-800\/10 {
  --tw-gradient-to: rgb(30 64 175 / 0.1) var(--tw-gradient-to-position);
}
.to-blue-800\/50 {
  --tw-gradient-to: rgb(30 64 175 / 0.5) var(--tw-gradient-to-position);
}
.to-cyan-600 {
  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);
}
.to-cyan-900\/20 {
  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);
}
.to-emerald-300 {
  --tw-gradient-to: #6ee7b7 var(--tw-gradient-to-position);
}
.to-emerald-500 {
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}
.to-emerald-500\/60 {
  --tw-gradient-to: rgb(16 185 129 / 0.6) var(--tw-gradient-to-position);
}
.to-emerald-800\/50 {
  --tw-gradient-to: rgb(6 95 70 / 0.5) var(--tw-gradient-to-position);
}
.to-fuchsia-500\/10 {
  --tw-gradient-to: rgb(217 70 239 / 0.1) var(--tw-gradient-to-position);
}
.to-gray-300 {
  --tw-gradient-to: #d1d5db var(--tw-gradient-to-position);
}
.to-gray-800 {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}
.to-gray-950 {
  --tw-gradient-to: #030712 var(--tw-gradient-to-position);
}
.to-gray-950\/75 {
  --tw-gradient-to: rgb(3 7 18 / 0.75) var(--tw-gradient-to-position);
}
.to-green-400 {
  --tw-gradient-to: #4ade80 var(--tw-gradient-to-position);
}
.to-green-800\/10 {
  --tw-gradient-to: rgb(22 101 52 / 0.1) var(--tw-gradient-to-position);
}
.to-green-800\/20 {
  --tw-gradient-to: rgb(22 101 52 / 0.2) var(--tw-gradient-to-position);
}
.to-green-900\/10 {
  --tw-gradient-to: rgb(20 83 45 / 0.1) var(--tw-gradient-to-position);
}
.to-indigo-500 {
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.to-indigo-500\/60 {
  --tw-gradient-to: rgb(99 102 241 / 0.6) var(--tw-gradient-to-position);
}
.to-indigo-500\/80 {
  --tw-gradient-to: rgb(99 102 241 / 0.8) var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-indigo-700 {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}
.to-indigo-800\/10 {
  --tw-gradient-to: rgb(55 48 163 / 0.1) var(--tw-gradient-to-position);
}
.to-indigo-800\/50 {
  --tw-gradient-to: rgb(55 48 163 / 0.5) var(--tw-gradient-to-position);
}
.to-indigo-900\/10 {
  --tw-gradient-to: rgb(49 46 129 / 0.1) var(--tw-gradient-to-position);
}
.to-indigo-900\/20 {
  --tw-gradient-to: rgb(49 46 129 / 0.2) var(--tw-gradient-to-position);
}
.to-orange-500\/5 {
  --tw-gradient-to: rgb(249 115 22 / 0.05) var(--tw-gradient-to-position);
}
.to-pink-500\/15 {
  --tw-gradient-to: rgb(236 72 153 / 0.15) var(--tw-gradient-to-position);
}
.to-pink-500\/20 {
  --tw-gradient-to: rgb(236 72 153 / 0.2) var(--tw-gradient-to-position);
}
.to-pink-500\/80 {
  --tw-gradient-to: rgb(236 72 153 / 0.8) var(--tw-gradient-to-position);
}
.to-pink-600 {
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}
.to-pink-600\/30 {
  --tw-gradient-to: rgb(219 39 119 / 0.3) var(--tw-gradient-to-position);
}
.to-pink-900\/10 {
  --tw-gradient-to: rgb(131 24 67 / 0.1) var(--tw-gradient-to-position);
}
.to-purple-100 {
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}
.to-purple-200 {
  --tw-gradient-to: #e9d5ff var(--tw-gradient-to-position);
}
.to-purple-400 {
  --tw-gradient-to: #c084fc var(--tw-gradient-to-position);
}
.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-purple-600\/30 {
  --tw-gradient-to: rgb(147 51 234 / 0.3) var(--tw-gradient-to-position);
}
.to-purple-800\/10 {
  --tw-gradient-to: rgb(107 33 168 / 0.1) var(--tw-gradient-to-position);
}
.to-purple-900\/10 {
  --tw-gradient-to: rgb(88 28 135 / 0.1) var(--tw-gradient-to-position);
}
.to-purple-900\/30 {
  --tw-gradient-to: rgb(88 28 135 / 0.3) var(--tw-gradient-to-position);
}
.to-purple-900\/60 {
  --tw-gradient-to: rgb(88 28 135 / 0.6) var(--tw-gradient-to-position);
}
.to-red-400 {
  --tw-gradient-to: #f87171 var(--tw-gradient-to-position);
}
.to-red-800\/20 {
  --tw-gradient-to: rgb(153 27 27 / 0.2) var(--tw-gradient-to-position);
}
.to-rose-500 {
  --tw-gradient-to: #f43f5e var(--tw-gradient-to-position);
}
.to-rose-500\/60 {
  --tw-gradient-to: rgb(244 63 94 / 0.6) var(--tw-gradient-to-position);
}
.to-rose-600 {
  --tw-gradient-to: #e11d48 var(--tw-gradient-to-position);
}
.to-rose-800\/50 {
  --tw-gradient-to: rgb(159 18 57 / 0.5) var(--tw-gradient-to-position);
}
.to-rose-900\/20 {
  --tw-gradient-to: rgb(136 19 55 / 0.2) var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #fff var(--tw-gradient-to-position);
}
.to-yellow-300 {
  --tw-gradient-to: #fde047 var(--tw-gradient-to-position);
}
.to-yellow-800\/10 {
  --tw-gradient-to: rgb(133 77 14 / 0.1) var(--tw-gradient-to-position);
}
.to-yellow-900\/10 {
  --tw-gradient-to: rgb(113 63 18 / 0.1) var(--tw-gradient-to-position);
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-3\.5 {
  padding: 0.875rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-1\.5 {
  padding-bottom: 0.375rem;
}
.pb-12 {
  padding-bottom: 3rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-12 {
  padding-left: 3rem;
}
.pl-14 {
  padding-left: 3.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pl-7 {
  padding-left: 1.75rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-0\.5 {
  padding-top: 0.125rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-12 {
  padding-top: 3rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-24 {
  padding-top: 6rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.align-middle {
  vertical-align: middle;
}
.align-bottom {
  vertical-align: bottom;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[11px\] {
  font-size: 11px;
}
.text-\[9px\] {
  font-size: 9px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-4 {
  line-height: 1rem;
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-snug {
  line-height: 1.375;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-amber-200 {
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.text-amber-400 {
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-fuchsia-400 {
  --tw-text-opacity: 1;
  color: rgb(232 121 249 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-100 {
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}
.text-green-200 {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.text-green-300 {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-indigo-300 {
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity, 1));
}
.text-indigo-400 {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(67 56 202 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-orange-300 {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.text-pink-100 {
  --tw-text-opacity: 1;
  color: rgb(252 231 243 / var(--tw-text-opacity, 1));
}
.text-pink-400 {
  --tw-text-opacity: 1;
  color: rgb(244 114 182 / var(--tw-text-opacity, 1));
}
.text-purple-100 {
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}
.text-purple-200 {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.text-purple-300 {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-red-100 {
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.text-red-200 {
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-violet-400 {
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/30 {
  color: rgb(255 255 255 / 0.3);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-yellow-100 {
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity, 1));
}
.text-yellow-200 {
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}
.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.caret-white {
  caret-color: #fff;
}
.opacity-0 {
  opacity: 0;
}
.opacity-10 {
  opacity: 0.1;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-40 {
  opacity: 0.4;
}
.opacity-5 {
  opacity: 0.05;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_15px_rgba\(80\2c 60\2c 200\2c 0\.15\)\] {
  --tw-shadow: 0 0 15px rgba(80,60,200,0.15);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_40px_rgba\(139\2c 92\2c 246\2c 0\.6\)\] {
  --tw-shadow: 0 0 40px rgba(139,92,246,0.6);
  --tw-shadow-colored: 0 0 40px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[inset_0_0_30px_rgba\(79\2c 70\2c 229\2c 0\.1\)\] {
  --tw-shadow: inset 0 0 30px rgba(79,70,229,0.1);
  --tw-shadow-colored: inset 0 0 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-neon-blue {
  --tw-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-neon-indigo {
  --tw-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-neon-purple {
  --tw-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-black\/20 {
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-900\/30 {
  --tw-shadow-color: rgb(30 58 138 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-900\/10 {
  --tw-shadow-color: rgb(20 83 45 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-900\/20 {
  --tw-shadow-color: rgb(20 83 45 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-900\/30 {
  --tw-shadow-color: rgb(20 83 45 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-indigo-900\/20 {
  --tw-shadow-color: rgb(49 46 129 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/20 {
  --tw-shadow-color: rgb(168 85 247 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/25 {
  --tw-shadow-color: rgb(168 85 247 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/5 {
  --tw-shadow-color: rgb(168 85 247 / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-900\/10 {
  --tw-shadow-color: rgb(88 28 135 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-900\/20 {
  --tw-shadow-color: rgb(88 28 135 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-900\/30 {
  --tw-shadow-color: rgb(88 28 135 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-900\/50 {
  --tw-shadow-color: rgb(88 28 135 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-red-900\/20 {
  --tw-shadow-color: rgb(127 29 29 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-yellow-900\/10 {
  --tw-shadow-color: rgb(113 63 18 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset {
  --tw-ring-inset: inset;
}
.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-blue-500\/50 {
  --tw-ring-color: rgb(59 130 246 / 0.5);
}
.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.ring-purple-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.ring-purple-500\/50 {
  --tw-ring-color: rgb(168 85 247 / 0.5);
}
.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}
.ring-opacity-60 {
  --tw-ring-opacity: 0.6;
}
.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}
.ring-offset-blue-400 {
  --tw-ring-offset-color: #60a5fa;
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-2xl {
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[100px\] {
  --tw-blur: blur(100px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[120px\] {
  --tw-blur: blur(120px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-\[80px\] {
  --tw-blur: blur(80px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-md {
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xs {
  --tw-backdrop-blur: blur(2px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-1000 {
  transition-delay: 1000ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.will-change-transform {
  will-change: transform;
}
/* Subtle text shadow for glowing effect */

:root {
  --foreground-rgb: 255, 255, 255;
  --background-color: 13, 13, 23;
  --background-hex: #0d0d17;
  --neon-purple: 139, 92, 246;
  --neon-pink: 236, 72, 153;
  --neon-blue: 59, 130, 246;
  --neon-indigo: 99, 102, 241;
  --neon-green: 74, 222, 128;
  --laser-purple: 128, 0, 255;
  --laser-pink: 236, 64, 122;
  --laser-blue: 30, 144, 255;
  --high-contrast-text: 240, 240, 252;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: var(--background-hex);
  position: relative;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Remove any body::before that might cause banding */
body::before {
  display: none !important;
}

/* =================== LASER NEON EFFECTS =================== */

/* Section containers with consistent backgrounds - NO TRANSPARENCY */
.laser-section {
  position: relative;
  border: 1px solid rgba(var(--laser-purple), 0.15);
  box-shadow: 0 0 20px rgba(var(--laser-purple), 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--background-hex) !important;
  background-image: none !important;
  transition: box-shadow 0.5s ease;
  margin-bottom: 2rem;
}

.laser-section:hover {
  box-shadow: 0 0 30px rgba(var(--laser-purple), 0.15);
}

/* Laser borders for cards - consistent background */
.laser-card {
  position: relative;
  border: 1px solid rgba(var(--laser-purple), 0.2);
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--background-hex);
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.1);
  transition: all 0.3s ease;
}

.laser-card:hover {
  box-shadow: 0 0 25px rgba(var(--laser-purple), 0.2);
  border-color: rgba(var(--laser-purple), 0.3);
}

/* Gradient buttons */
.laser-button {
  position: relative;
  background: linear-gradient(90deg, rgba(var(--laser-purple), 0.8), rgba(var(--laser-pink), 0.8));
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.4);
  z-index: 1;
}

.laser-button:hover {
  box-shadow: 0 0 20px rgba(var(--laser-purple), 0.6);
  transform: translateY(-2px);
}

/* Gradient text */
.laser-gradient-text {
  background: linear-gradient(90deg, rgba(var(--laser-purple), 1) 0%, rgba(var(--laser-pink), 1) 50%, rgba(var(--laser-blue), 1) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.laser-gradient-text::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  background: linear-gradient(90deg, rgba(var(--laser-purple), 1) 0%, rgba(var(--laser-pink), 1) 50%, rgba(var(--laser-blue), 1) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  filter: blur(6px);
  opacity: 0.5;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Neon flicker animation */
.laser-flicker {
  animation: flicker 3s linear infinite;
}

@keyframes flicker {
  0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100% {
    opacity: 1;
  }
  20%, 21.999%, 63%, 63.999%, 65%, 69.999% {
    opacity: 0.5;
  }
}

/* Microinteractions - Icon glow/pulse hover effect */
.laser-icon {
  transition: all 0.3s ease;
}

.laser-icon:hover {
  transform: scale(1.15);
  filter: drop-shadow(0 0 8px rgba(var(--laser-purple), 0.8));
}

/* Animated laser particles with fixed background */
.laser-particles-container {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
  opacity: 0.3;
  background-color: var(--background-hex);
}

.laser-particle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.2;
  filter: blur(3px);
  pointer-events: none;
  animation: laser-float linear infinite;
}

.laser-particle-purple {
  background: rgba(var(--laser-purple), 0.4);
  box-shadow: 0 0 10px rgba(var(--laser-purple), 0.5);
}

.laser-particle-pink {
  background: rgba(var(--laser-pink), 0.4);
  box-shadow: 0 0 10px rgba(var(--laser-pink), 0.5);
}

.laser-particle-blue {
  background: rgba(var(--laser-blue), 0.4);
  box-shadow: 0 0 10px rgba(var(--laser-blue), 0.5);
}

@keyframes laser-float {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.2;
  }
  90% {
    opacity: 0.2;
  }
  100% {
    transform: translateY(var(--y-end)) translateX(var(--x-end));
    opacity: 0;
  }
}

/* Neon section divider */
.laser-divider {
  height: 1px;
  width: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--laser-purple), 0.5), transparent);
  position: relative;
  margin: 3rem 0;
}

.laser-divider::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, rgba(var(--laser-purple), 0.2), transparent);
  filter: blur(2px);
}

/* Scroll reveal animations */
.scroll-reveal {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Neon focus glow for inputs */
.laser-input {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(var(--laser-purple), 0.2);
  border-radius: 0.375rem;
  color: white;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.laser-input:focus {
  outline: none;
  border-color: rgba(var(--laser-purple), 0.5);
  box-shadow: 0 0 0 3px rgba(var(--laser-purple), 0.25);
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .gradient-text,
.rtl .gradient-button {
  direction: rtl;
}

/* Neo-Futuristic Neon Effects */
.neon-text-outline {
  position: relative;
  z-index: 1;
  text-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.5),
    0 0 10px rgba(var(--neon-purple), 0.3),
    0 0 20px rgba(var(--neon-purple), 0.1);
  transition: text-shadow 0.3s ease-in-out;
}

.neon-text-outline:hover {
  text-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.7),
    0 0 10px rgba(var(--neon-purple), 0.5),
    0 0 20px rgba(var(--neon-purple), 0.3),
    0 0 30px rgba(var(--neon-purple), 0.1);
}

.neon-border {
  position: relative;
  border: 1px solid rgba(var(--neon-purple), 0.3);
  box-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.3),
    0 0 10px rgba(var(--neon-purple), 0.2),
    inset 0 0 5px rgba(var(--neon-purple), 0.1);
  transition: all 0.3s ease;
}

.neon-border:hover {
  border-color: rgba(var(--neon-purple), 0.6);
  box-shadow: 
    0 0 5px rgba(var(--neon-purple), 0.5),
    0 0 10px rgba(var(--neon-purple), 0.3),
    0 0 15px rgba(var(--neon-purple), 0.1),
    inset 0 0 5px rgba(var(--neon-purple), 0.2);
}

.neon-card {
  background: rgba(15, 23, 42, 0.7);
  border-radius: 0.5rem;
  border: 1px solid rgba(var(--neon-purple), 0.2);
  box-shadow: 
    0 0 10px rgba(var(--neon-purple), 0.1),
    inset 0 0 5px rgba(var(--neon-purple), 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.neon-card:hover {
  transform: translateY(-3px);
  border-color: rgba(var(--neon-purple), 0.4);
  box-shadow: 
    0 0 15px rgba(var(--neon-purple), 0.2),
    inset 0 0 10px rgba(var(--neon-purple), 0.1);
}

.neon-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(var(--neon-purple), 0.05),
    transparent
  );
  transition: all 0.5s ease;
}

.neon-card:hover::after {
  left: 100%;
}

.animated-bg-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.15;
  filter: blur(40px);
  animation: float-around 20s linear infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes float-around {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  25%, 50%, 75% {
    transform: translate(0, 0) scale(1);
  }
}

.neon-button {
  position: relative;
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(var(--neon-purple), 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  transition: all 0.2s ease;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(var(--neon-purple), 0.2);
  z-index: 1;
}

.neon-button:hover {
  background: rgba(20, 30, 50, 0.8);
  border-color: rgba(var(--neon-purple), 0.6);
  box-shadow: 
    0 0 15px rgba(var(--neon-purple), 0.4),
    0 0 30px rgba(var(--neon-purple), 0.2);
  transform: translateY(-2px);
}

.neon-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(var(--neon-purple), 0.2),
    transparent
  );
  transition: left 0.5s ease;
  z-index: -1;
}

.neon-button:hover::before {
  left: 100%;
}

/* Floating particles */
.floating-particles {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.floating-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(var(--neon-purple), 0.5);
  filter: blur(1px);
  animation: float-particle var(--duration, 15s) ease-in-out infinite;
  opacity: var(--opacity, 0.3);
}

@keyframes float-particle {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: var(--opacity, 0.3);
  }
  25% {
    transform: translate3d(var(--x1, 50px), var(--y1, -30px), 0) scale(1.2);
    opacity: var(--opacity-mid, 0.4);
  }
  50% {
    transform: translate3d(var(--x2, 100px), var(--y2, 50px), 0) scale(1);
    opacity: var(--opacity-peak, 0.5);
  }
  75% {
    transform: translate3d(var(--x3, 50px), var(--y3, 100px), 0) scale(0.8);
    opacity: var(--opacity-mid, 0.4);
  }
}

/* Animated neon glow for icons */
.neon-icon {
  position: relative;
  transition: all 0.3s ease;
}

.neon-icon:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 0 5px rgba(var(--neon-purple), 0.7));
}

.neon-icon-pulse {
  animation: neon-pulse 2s ease-in-out infinite;
}

@keyframes neon-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(var(--neon-purple), 0.5));
  }
  50% {
    filter: drop-shadow(0 0 5px rgba(var(--neon-purple), 0.8));
  }
}

/* Enhanced section transitions */
.neon-section-divider {
  position: relative;
  height: 1px;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(var(--neon-purple), 0.3), 
    transparent
  );
  margin: 2rem 0;
  width: 100%;
}

.neon-section-divider::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(var(--neon-purple), 0.05),
    transparent
  );
  pointer-events: none;
}

/* Section background gradients */
.neon-bg-glow {
  position: relative;
  overflow: hidden;
}

.neon-bg-glow::before {
  content: '';
  position: absolute;
  top: 30%;
  left: -10%;
  width: 50%;
  height: 70%;
  background: radial-gradient(
    circle,
    rgba(var(--neon-purple), 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 0;
}

.neon-bg-glow:hover::before {
  opacity: 1;
}

/* Gradients */
.gradient-text {
  background: linear-gradient(90deg, #a78bfa, #818cf8, #60a5fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.gradient-button {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed, #6d28d9);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(123, 97, 255, 0.15);
  animation: pulse-glow 3s infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

.gradient-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.35);
}

/* Non-animated button styles */
.static-button {
  background: #7c3aed;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(123, 97, 255, 0.15);
}

.static-button:hover {
  background: #6d28d9;
}

/* Dots effects */
.dot-blue {
  background-color: #60a5fa;
  transition: transform 0.3s ease;
}

.dot-purple {
  background-color: #8b5cf6;
  transition: transform 0.3s ease;
}

.dot-pink {
  background-color: #ec4899;
  transition: transform 0.3s ease;
}

.dots-container:hover .dot {
  transform: scale(1.3);
}

/* Navigation links */
.nav-link {
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(90deg, #8b5cf6, #60a5fa);
  transition: width 0.3s ease;
}

.rtl .nav-link::after {
  left: auto;
  right: 0;
}

.nav-link:hover::after {
  width: 100%;
}

/* AI Badge */
.ai-badge {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.ai-badge:hover {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

.ai-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(139, 92, 246, 0), 
    rgba(139, 92, 246, 0.2), 
    rgba(139, 92, 246, 0));
  animation: pulse 2s infinite;
}

.rtl .ai-badge::before {
  left: auto;
  right: -100%;
  transform: scaleX(-1);
  animation: pulse-rtl 2s infinite;
}

@keyframes pulse {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes pulse-rtl {
  0% {
    right: -100%;
  }
  50% {
    right: 100%;
  }
  100% {
    right: 100%;
  }
}

/* Shake effect */
.shake-on-hover:hover {
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, -1px, 0);
  }
  
  20%, 80% {
    transform: translate3d(2px, 2px, 0);
  }

  30%, 50%, 70% {
    transform: translate3d(-2px, -2px, 0);
  }

  40%, 60% {
    transform: translate3d(2px, 2px, 0);
  }
}

/* Glassmorphism */
.glass-effect {
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  background-color: rgba(13, 13, 23, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Fix for Safari */
@supports not ((-webkit-backdrop-filter: blur(8px)) or (backdrop-filter: blur(8px))) {
  .glass-effect {
    background-color: rgba(13, 13, 23, 0.9);
  }
}

/* Add after existing styles */

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Custom scrollbar for a futuristic look */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.7);
}

/* Ambient glow effects */
.glow-blue {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
}

.glow-purple {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

.glow-green {
  box-shadow: 0 0 15px rgba(74, 222, 128, 0.5);
}

.glow-yellow {
  box-shadow: 0 0 15px rgba(250, 204, 21, 0.5);
}

.glow-indigo {
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-glow {
  animation: pulse-glow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Dashboard specific styles */
.bg-grid-pattern {
  background-size: 20px 20px;
  background-image: linear-gradient(to right, rgba(30, 41, 59, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(30, 41, 59, 0.3) 1px, transparent 1px);
}

.shadow-neon-blue {
  box-shadow: 0 0 15px -5px rgba(59, 130, 246, 0.3);
}

.shadow-neon-purple {
  box-shadow: 0 0 15px -5px rgba(147, 51, 234, 0.3);
}

.shadow-neon-green {
  box-shadow: 0 0 15px -5px rgba(16, 185, 129, 0.3);
}

.shadow-neon-indigo {
  box-shadow: 0 0 15px -5px rgba(99, 102, 241, 0.3);
}

.shadow-neon-yellow {
  box-shadow: 0 0 15px -5px rgba(245, 158, 11, 0.3);
}

/* Glass effect for cards */
.backdrop-blur-xs {
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

/* Dashboard Card Styles - Enhanced for consistency */
.dashboard-card {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: rgb(31 41 55 / 0.6);
  background-color: rgb(17 24 39 / 0.9);
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
.dashboard-card:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-color: rgb(55 65 81 / 0.8);
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transform: translateY(-2px) scale(1.01);
}

.dashboard-card-blue {
  border-color: rgb(29 78 216 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(30 58 138 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0.9) var(--tw-gradient-to-position);
}

.dashboard-card-purple {
  border-color: rgb(126 34 206 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(88 28 135 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0.9) var(--tw-gradient-to-position);
}

.dashboard-card-green {
  border-color: rgb(21 128 61 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(20 83 45 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0.9) var(--tw-gradient-to-position);
}

.dashboard-card-indigo {
  border-color: rgb(67 56 202 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(49 46 129 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(49 46 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0.9) var(--tw-gradient-to-position);
}

.dashboard-card-yellow {
  border-color: rgb(161 98 7 / 0.3);
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  --tw-gradient-from: rgb(113 63 18 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: rgb(15 23 42 / 0.9) var(--tw-gradient-to-position);
}

.dashboard-card-header {
  margin-bottom: 1rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
}

@media (max-width: 767px) {
  @media (max-width: 640px) {
    
    .testimonial-card .dashboard-card-header.items-center {
      margin-top: 1rem;
    }
    
    .testimonial-card .flex.dashboard-card-header {
      margin-top: 1rem;
    }
  }
}

.dashboard-card-title {
  margin-right: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}

@media (max-width: 767px) {
  @media (max-width: 640px) {
    
    .testimonial-card .dashboard-card-title.items-center {
      margin-top: 1rem;
    }
    
    .testimonial-card .flex.dashboard-card-title {
      margin-top: 1rem;
    }
  }
}

.dashboard-card-title-icon {
  margin-right: 0.5rem;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  padding: 0.375rem;
}

@media (max-width: 767px) {
  @media (max-width: 640px) {
    
    .testimonial-card .dashboard-card-title-icon.items-center {
      margin-top: 1rem;
    }
    
    .testimonial-card .flex.dashboard-card-title-icon {
      margin-top: 1rem;
    }
  }
}

.dashboard-card-title-icon {
  width: 32px;
  height: 32px;
}

/* Chart Container Styles */
.chart-container {
  position: relative;
  height: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: rgb(30 41 59 / 0.5);
  background-color: rgb(2 6 23 / 0.4);
  padding: 0.75rem;
  min-height: 240px;
}

/* Mobile Responsive Adjustments */
@media (max-width: 640px) {
  .chart-container {
    min-height: 200px;
  }
}

/* Card Glow Effects */
.card-glow {
  pointer-events: none;
  position: absolute;
  inset: 0px;
  opacity: 0;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
}

.dashboard-card:hover .card-glow {
  opacity: 1;
}

/* Fancy Card Border Glow */
.border-glow {
  position: relative;
  overflow: hidden;
}

.border-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 0.8rem;
  filter: blur(8px);
}

.border-glow:hover::before {
  opacity: 1;
}

.border-glow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.75rem;
  padding: 1.5px;
  background: linear-gradient(
    135deg,
    rgba(60, 60, 80, 0.5) 0%,
    rgba(160, 160, 200, 0.3) 50%,
    rgba(60, 60, 80, 0.5) 100%
  );
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  z-index: 1;
}

.border-glow:hover::after {
  background: linear-gradient(135deg, rgba(80, 80, 120, 0.8) 0%, rgba(180, 180, 240, 0.6) 50%, rgba(80, 80, 120, 0.8) 100%);
}

/* Shadow Effects for Cards */
.shadow-neon-blue {
  box-shadow: 0 0 15px -5px rgba(59, 130, 246, 0.3);
}

.shadow-neon-purple {
  box-shadow: 0 0 15px -5px rgba(147, 51, 234, 0.3);
}

.shadow-neon-green {
  box-shadow: 0 0 15px -5px rgba(16, 185, 129, 0.3);
}

.shadow-neon-yellow {
  box-shadow: 0 0 15px -5px rgba(245, 158, 11, 0.3);
}

.shadow-neon-indigo {
  box-shadow: 0 0 15px -5px rgba(99, 102, 241, 0.3);
}

/* Metric card sizing helpers */
.metric-card-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  grid-auto-rows: minmax(140px, auto);
}

.metric-card {
  display: flex;
  height: 100%;
  min-height: 140px;
  flex-direction: column;
}

@media (max-width: 767px) {
  @media (max-width: 640px) {
    
    .testimonial-card .metric-card.items-center {
      margin-top: 1rem;
    }
  }
}

/* Chart panel sizing helpers */
.chart-panel-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  grid-auto-rows: minmax(350px, auto);
}

.chart-panel {
  display: flex;
  height: 100%;
  min-height: 350px;
  flex-direction: column;
}

@media (max-width: 767px) {
  @media (max-width: 640px) {
    
    .testimonial-card .chart-panel.items-center {
      margin-top: 1rem;
    }
  }
}

/* Card Title Icons */
.title-icon-blue {
  margin-right: 0.5rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: rgb(59 130 246 / 0.2);
  background-color: rgb(30 58 138 / 0.4);
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.title-icon-purple {
  margin-right: 0.5rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: rgb(168 85 247 / 0.2);
  background-color: rgb(88 28 135 / 0.4);
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.title-icon-green {
  margin-right: 0.5rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: rgb(34 197 94 / 0.2);
  background-color: rgb(20 83 45 / 0.4);
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.title-icon-indigo {
  margin-right: 0.5rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: rgb(99 102 241 / 0.2);
  background-color: rgb(49 46 129 / 0.4);
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.title-icon-yellow {
  margin-right: 0.5rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: rgb(234 179 8 / 0.2);
  background-color: rgb(113 63 18 / 0.4);
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

/* Improved text contrast styles */
.high-contrast-text {
  color: rgb(var(--high-contrast-text));
}

.caption-text {
  color: rgba(var(--high-contrast-text), 0.85);
  font-weight: 500;
  letter-spacing: 0.01em;
}

.subheading-text {
  color: rgba(var(--high-contrast-text), 0.9);
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* Enhanced section spacing */
.section-spacing {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

@media (min-width: 768px) {
  .section-spacing {
    margin-top: 4rem;
    margin-bottom: 4rem;
  }
}

/* Primary and secondary CTA styles */
.primary-cta {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  color: white;
  background: linear-gradient(to right, #8b5cf6, #6366f1);
  border-radius: 9999px;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.25);
  border: 2px solid transparent;
  z-index: 1;
}

.primary-cta:hover {
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.35);
  transform: translateY(-2px);
}

.secondary-cta {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  color: white;
  background-color: transparent;
  border: 2px solid rgba(139, 92, 246, 0.6);
  border-radius: 9999px;
  transition: all 0.3s ease;
  overflow: hidden;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.secondary-cta:hover {
  background-color: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.9);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
  transform: translateY(-2px);
}

/* Enhanced information hierarchy */
.heading-xl {
  font-size: 2.75rem;
  line-height: 1.1;
  font-weight: 800;
  margin-bottom: 1.5rem;
  letter-spacing: -0.03em;
}

.heading-lg {
  font-size: 2.25rem;
  line-height: 1.2;
  font-weight: 700;
  margin-bottom: 1.25rem;
  letter-spacing: -0.02em;
}

.heading-md {
  font-size: 1.75rem;
  line-height: 1.3;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.01em;
}

@media (min-width: 768px) {
  .heading-xl {
    font-size: 3.75rem;
  }
  
  .heading-lg {
    font-size: 3rem;
  }
  
  .heading-md {
    font-size: 2.25rem;
  }
}

/* Feature highlight styles */
.feature-highlight {
  border-left: 3px solid rgba(var(--laser-purple), 0.7);
  padding-left: 1rem;
  background: linear-gradient(90deg, 
    rgba(var(--laser-purple), 0.1) 0%, 
    rgba(var(--laser-purple), 0) 100%
  );
}

/* Improved carousel navigation */
.carousel-nav {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(var(--high-contrast-text), 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.carousel-dot.active {
  background-color: rgba(var(--laser-purple), 0.9);
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(var(--laser-purple), 0.5);
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(var(--laser-purple), 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(var(--laser-purple), 0.3);
  transition: all 0.3s ease;
  z-index: 10;
}

.carousel-arrow:hover {
  background: rgba(var(--laser-purple), 0.4);
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.5);
}

.carousel-arrow-left {
  left: -5px;
}

.carousel-arrow-right {
  right: -5px;
}

@media (min-width: 768px) {
  .carousel-arrow-left {
    left: -20px;
  }
  
  .carousel-arrow-right {
    right: -20px;
  }
}

/* Pricing toggle styles */
.pricing-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(var(--laser-purple), 0.15);
  padding: 0.5rem;
  border-radius: 9999px;
  margin: 2rem auto;
}

.pricing-toggle-option {
  padding: 0.5rem 1.5rem;
  border-radius: 9999px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.pricing-toggle-highlight {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  height: calc(100% - 1rem);
  border-radius: 9999px;
  background: linear-gradient(90deg, rgba(var(--laser-purple), 0.7), rgba(var(--laser-pink), 0.7));
  transition: all 0.3s ease;
  z-index: 1;
  box-shadow: 0 0 15px rgba(var(--laser-purple), 0.5);
}

/* Animation for slide in effect */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gentlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.2);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-pulse-subtle {
  animation: gentlePulse 2s infinite;
}

/* Enhanced conversion rate indicator */
.conversion-change-indicator {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.conversion-change-indicator.positive {
  background-color: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: rgb(74, 222, 128);
}

.conversion-change-indicator.negative {
  background-color: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: rgb(248, 113, 113);
}

@keyframes gentlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.2);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -500px 0;
  }
  100% {
    background-position: 500px 0;
  }
}

.chart-container {
  position: relative;
  overflow: hidden;
}

.chart-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(165,180,252,0.05) 50%, rgba(255,255,255,0) 100%);
  background-size: 200% 100%;
  animation: shimmer 4s infinite linear;
  pointer-events: none;
}

.conversion-change-indicator {
  /* ... existing code ... */
}

/* Add this new class for unified sections without borders and shadows */
.unified-section {
  position: relative;
  overflow: hidden;
  padding: 1rem 0;
  margin-bottom: 2rem;
  background-color: transparent !important;
  background-image: none !important;
  z-index: 1;
}

/* Mobile Responsiveness Fixes */
@media (max-width: 767px) {
  /* Fix navbar spacing and alignment on mobile */
  motion.nav, nav {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    max-width: 92% !important;
  }

  /* Ensure the logo and text are properly aligned */
  .nav-logo {
    margin-right: 0.25rem !important;
  }
  
  /* Fix alignment of navbar elements on mobile */
  .static-button {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    font-size: 0.75rem !important;
  }
  
  /* Make the navbar more compact */
  nav {
    padding-top: 0.35rem !important;
    padding-bottom: 0.35rem !important;
  }

  /* Fix hero section text for better readability on mobile */
  .heading-xl {
    font-size: 2rem !important;
    line-height: 1.2 !important;
    letter-spacing: -0.02em !important;
    margin-bottom: 1rem !important;
  }

  .subheading-text {
    font-size: 1rem !important;
    line-height: 1.5 !important;
    margin-bottom: 1.5rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  /* Fix spacing for CTA buttons in hero section */
  .hero-cta-container {
    gap: 1rem !important;
    margin-bottom: 2rem !important;
  }

  /* Feature cards fixes for mobile */
  .features-grid {
    gap: 1.5rem !important;
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  .feature-card {
    min-height: auto !important;
    height: auto !important;
    padding: 1.5rem !important;
  }

  .feature-card-icon {
    margin-bottom: 1rem !important;
  }

  .feature-card-highlights {
    margin-top: 1rem !important;
  }

  /* Pricing section mobile fixes */
  .pricing-container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .pricing-card {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
  }

  .pricing-card-features {
    margin-top: 1rem !important;
    padding-left: 0.5rem !important;
  }

  .pricing-card-feature {
    margin-bottom: 0.5rem !important;
  }

  .pricing-card-button {
    width: 100% !important;
    margin-top: 1rem !important;
  }

  /* Testimonial section mobile fixes */
  .testimonial-card {
    padding: 1.25rem !important;
    min-height: auto !important;
    margin-bottom: 1rem !important;
    max-width: 100% !important;
    border-radius: 1rem !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  }

  .testimonial-content {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
  }

  .testimonial-author {
    margin-top: 1rem !important;
  }

  /* Mobile testimonial fixes */
  @media (max-width: 640px) {
    .testimonial-card {
      background: linear-gradient(to bottom, #0f0f1f, #1a1a35) !important;
      border: 1px solid rgba(139, 92, 246, 0.2) !important;
    }
    
    .testimonial-card p {
      font-size: 0.875rem !important;
      line-height: 1.5 !important;
    }
    
    .testimonial-card .flex.items-center {
      margin-top: 1rem !important;
    }
    
    #testimonials h2 {
      font-size: 1.75rem !important;
      line-height: 1.3 !important;
    }
    
    #testimonials .subheading-text {
      font-size: 0.875rem !important;
      line-height: 1.4 !important;
      margin-bottom: 1.5rem !important;
    }
  }

  /* General container padding fixes */
  .section-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Add classes to specific components that need unique fixes */
.feature-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.feature-description {
  flex: 1;
}

.pricing-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pricing-content {
  flex: 1;
}

.testimonial-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.testimonial-content-wrapper {
  flex: 1;
}

/* Loading pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.85);
  }
  50% {
    opacity: 0.7;
    transform: scale(1);
  }
}

.loading-pulse {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5 0%, #8b5cf6 100%);
  animation: pulse 1.5s ease-in-out infinite;
}

/* Hero Animation Styles */
@keyframes soundWave {
  0% {
    height: 10px;
    opacity: 0.5;
  }
  50% {
    height: 35px;
    opacity: 0.8;
  }
  100% {
    height: 10px;
    opacity: 0.5;
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-15px) rotate(5deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) rotate(0);
    opacity: 0.6;
  }
}

.phone-frame {
  animation: float 6s ease-in-out infinite;
}

.phone-body {
  box-shadow: 
    0 0 20px rgba(168, 85, 247, 0.3),
    0 0 60px rgba(168, 85, 247, 0.1),
    inset 0 0 4px rgba(168, 85, 247, 0.2);
  transition: all 0.3s ease;
}

.phone-body:hover {
  box-shadow: 
    0 0 25px rgba(168, 85, 247, 0.5),
    0 0 80px rgba(168, 85, 247, 0.2),
    inset 0 0 8px rgba(168, 85, 247, 0.3);
  transform: scale(1.03);
}

.hero-animation-container {
  perspective: 1000px;
}

/* Futuristic Cyberpunk Phone Animation Styles */
.tech-circle {
  position: relative;
  animation: rotate 20s linear infinite;
}

.tech-circle::before,
.tech-circle::after {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 50%;
  border: 1px dashed rgba(139, 92, 246, 0.4);
  animation: rotate 30s linear reverse infinite;
}

.tech-circle::after {
  inset: -8px;
  opacity: 0.3;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.data-node {
  animation: pulseGlow 2s ease-in-out infinite alternate;
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.6);
  z-index: 20;
}

@keyframes pulseGlow {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.4);
  }
  100% {
    transform: scale(1.3);
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.8);
  }
}

.scanner-line {
  top: -10%;
  animation: scanline 2s ease-in-out infinite;
  opacity: 0.7;
}

@keyframes scanline {
  0% {
    top: -10%;
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.8;
  }
  100% {
    top: 110%;
    opacity: 0;
  }
}

.animate-pulse-slow {
  animation: pulseSlow 4s ease-in-out infinite;
}

@keyframes pulseSlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-faint {
  animation: pulseFaint 1.5s ease-in-out infinite;
}

@keyframes pulseFaint {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

.voice-waveform .w-1 {
  animation: voiceWave 0.5s ease-in-out infinite alternate;
}

@keyframes voiceWave {
  0% {
    height: 10%;
  }
  50% {
    height: 70%;
  }
  100% {
    height: 30%;
  }
}

.rotating-analysis-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px dashed rgba(139, 92, 246, 0.6);
  transform: translate(-50%, -50%);
  animation: rotateRing 4s linear infinite;
}

.rotating-analysis-ring:nth-child(2) {
  width: 55px;
  height: 55px;
  border-color: rgba(236, 72, 153, 0.5);
  animation-duration: 5s;
  animation-direction: reverse;
}

@keyframes rotateRing {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.pulse-grow {
  animation: pulseGrow 2s ease-in-out infinite alternate;
}

@keyframes pulseGrow {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.4);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.7);
  }
}

.pulse-subtle {
  animation: pulseSubtle 1.5s ease-in-out infinite;
}

@keyframes pulseSubtle {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.shadow-neon-red {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
}

.shadow-neon-green {
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.5);
}

.tech-border {
  position: relative;
  overflow: hidden;
}

.tech-border::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: conic-gradient(
    transparent, 
    rgba(139, 92, 246, 0.3),
    transparent 30%
  );
  animation: borderSpin 8s linear infinite;
}

@keyframes borderSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.sound-bar {
  position: relative;
  overflow: hidden;
}

.sound-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, transparent, rgba(255, 255, 255, 0.4));
}

@keyframes dataFloat {
  0% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  50% {
    transform: translateY(-20px) rotate(180deg) scale(1.3);
  }
  100% {
    transform: translateY(0) rotate(360deg) scale(1);
  }
}

.data-particle {
  filter: blur(1px);
  z-index: 5;
}

.drop-shadow-glow {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.6));
}

.holo-overlay {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.05) 0%, 
    rgba(59, 130, 246, 0.05) 50%,
    rgba(236, 72, 153, 0.05) 100%
  );
  mix-blend-mode: overlay;
  z-index: 30;
}

.holo-overlay::before {
  content: '';
  position: absolute;
  inset: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    rgba(139, 92, 246, 0.08) 1px,
    transparent 2px
  );
  background-size: 100% 4px;
  opacity: 0.3;
  z-index: 31;
  pointer-events: none;
}

.scanlines {
  background: repeating-linear-gradient(
    0deg,
    transparent,
    rgba(139, 92, 246, 0.05) 1px,
    transparent 2px
  );
  background-size: 100% 4px;
  mix-blend-mode: overlay;
  animation: scanAnim 8s linear infinite;
  opacity: 0.4;
  z-index: 35;
}

@keyframes scanAnim {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 100px;
  }
}

.code-stream {
  animation: codeStream 20s linear infinite;
  opacity: 0.7;
  z-index: 2;
}

@keyframes codeStream {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(200%) rotate(30deg);
  }
}

/* Orbit Icons Animation */
.orbit-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: orbit var(--orbit-duration, 15s) linear infinite;
  animation-delay: var(--orbit-delay, 0s);
  z-index: 15;
}

@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(120px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(120px) rotate(-360deg);
  }
}

.shadow-glow {
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
}

/* AI Core Ring */
.ai-core-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid rgba(139, 92, 246, 0.6);
  border-left: 2px solid rgba(236, 72, 153, 0.6);
  border-bottom: 2px solid rgba(59, 130, 246, 0.6);
  animation: coreRingRotate 15s linear infinite;
}

@keyframes coreRingRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulse Rings */
.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid rgba(139, 92, 246, 0.5);
  border-right: 2px solid rgba(236, 72, 153, 0.5);
  opacity: 0;
  animation: pulseRingExpand 4s ease-out infinite;
}

@keyframes pulseRingExpand {
  0% {
    width: 150px;
    height: 150px;
    opacity: 0.7;
    border-width: 3px;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
    border-width: 1px;
  }
}

/* Workflow Item Animations */
.workflow-item {
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-workflow-sequence .workflow-item:nth-child(1) {
  animation-delay: 0.3s;
}

.ai-workflow-sequence .workflow-item:nth-child(2) {
  animation-delay: 0.9s;
}

.ai-workflow-sequence .workflow-item:nth-child(3) {
  animation-delay: 1.5s;
}

.ai-workflow-sequence .workflow-item:nth-child(4) {
  animation-delay: 2.1s;
}

/* Add new animation for floating particles */
@keyframes floatParticle {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10px, 10px);
  }
  50% {
    transform: translate(0, 20px);
  }
  75% {
    transform: translate(-10px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

/* Enhanced pulse animation for ambient lighting */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

/* Animation delay classes to fix hydration mismatch */
.animate-pulse-slow-delay-1 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 1s;
}

.animate-pulse-slow-delay-2 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-pulse-slow-delay-0-5 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 0.5s;
}

.animate-pulse-slow-delay-1-5 {
  animation: pulse-slow 8s ease-in-out infinite;
  animation-delay: 1.5s;
}

.file\:mr-4::file-selector-button {
  margin-right: 1rem;
}

.file\:rounded-md::file-selector-button {
  border-radius: 0.375rem;
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-blue-50::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.file\:px-4::file-selector-button {
  padding-left: 1rem;
  padding-right: 1rem;
}

.file\:py-2::file-selector-button {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-blue-700::file-selector-button {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.placeholder\:text-gray-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.placeholder\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.before\:shadow-neon-blue::before {
  content: var(--tw-content);
  --tw-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:shadow-neon-green::before {
  content: var(--tw-content);
  --tw-shadow: 0 0 15px rgba(74, 222, 128, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:shadow-neon-indigo::before {
  content: var(--tw-content);
  --tw-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:shadow-neon-purple::before {
  content: var(--tw-content);
  --tw-shadow: 0 0 15px rgba(139, 92, 246, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.before\:shadow-neon-yellow::before {
  content: var(--tw-content);
  --tw-shadow: 0 0 15px rgba(250, 204, 21, 0.5);
  --tw-shadow-colored: 0 0 15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}

.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}

.hover\:translate-y-\[-2px\]:hover {
  --tw-translate-y: -2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.01\]:hover {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border:hover {
  border-width: 1px;
}

.hover\:border-b-2:hover {
  border-bottom-width: 2px;
}

.hover\:border-fuchsia-500\/30:hover {
  border-color: rgb(217 70 239 / 0.3);
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-600\/50:hover {
  border-color: rgb(75 85 99 / 0.5);
}

.hover\:border-gray-600\/70:hover {
  border-color: rgb(75 85 99 / 0.7);
}

.hover\:border-gray-700\/50:hover {
  border-color: rgb(55 65 81 / 0.5);
}

.hover\:border-green-500\/30:hover {
  border-color: rgb(34 197 94 / 0.3);
}

.hover\:border-green-500\/50:hover {
  border-color: rgb(34 197 94 / 0.5);
}

.hover\:border-indigo-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-500\/30:hover {
  border-color: rgb(99 102 241 / 0.3);
}

.hover\:border-indigo-500\/40:hover {
  border-color: rgb(99 102 241 / 0.4);
}

.hover\:border-purple-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-400\/30:hover {
  border-color: rgb(192 132 252 / 0.3);
}

.hover\:border-purple-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-500\/30:hover {
  border-color: rgb(168 85 247 / 0.3);
}

.hover\:border-purple-500\/50:hover {
  border-color: rgb(168 85 247 / 0.5);
}

.hover\:border-red-500\/30:hover {
  border-color: rgb(239 68 68 / 0.3);
}

.hover\:border-red-500\/50:hover {
  border-color: rgb(239 68 68 / 0.5);
}

.hover\:border-violet-500\/30:hover {
  border-color: rgb(139 92 246 / 0.3);
}

.hover\:border-yellow-500\/50:hover {
  border-color: rgb(234 179 8 / 0.5);
}

.hover\:bg-\[\#33334d\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(51 51 77 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600\/70:hover {
  background-color: rgb(37 99 235 / 0.7);
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700\/80:hover {
  background-color: rgb(29 78 216 / 0.8);
}

.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700\/30:hover {
  background-color: rgb(55 65 81 / 0.3);
}

.hover\:bg-gray-700\/40:hover {
  background-color: rgb(55 65 81 / 0.4);
}

.hover\:bg-gray-700\/50:hover {
  background-color: rgb(55 65 81 / 0.5);
}

.hover\:bg-gray-700\/70:hover {
  background-color: rgb(55 65 81 / 0.7);
}

.hover\:bg-gray-700\/80:hover {
  background-color: rgb(55 65 81 / 0.8);
}

.hover\:bg-gray-750:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(45 55 72 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800\/30:hover {
  background-color: rgb(31 41 55 / 0.3);
}

.hover\:bg-gray-800\/50:hover {
  background-color: rgb(31 41 55 / 0.5);
}

.hover\:bg-gray-800\/60:hover {
  background-color: rgb(31 41 55 / 0.6);
}

.hover\:bg-gray-800\/80:hover {
  background-color: rgb(31 41 55 / 0.8);
}

.hover\:bg-gray-800\/90:hover {
  background-color: rgb(31 41 55 / 0.9);
}

.hover\:bg-green-500\/30:hover {
  background-color: rgb(34 197 94 / 0.3);
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600\/30:hover {
  background-color: rgb(22 163 74 / 0.3);
}

.hover\:bg-green-600\/70:hover {
  background-color: rgb(22 163 74 / 0.7);
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-600\/50:hover {
  background-color: rgb(79 70 229 / 0.5);
}

.hover\:bg-indigo-600\/80:hover {
  background-color: rgb(79 70 229 / 0.8);
}

.hover\:bg-indigo-600\/90:hover {
  background-color: rgb(79 70 229 / 0.9);
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500\/10:hover {
  background-color: rgb(168 85 247 / 0.1);
}

.hover\:bg-purple-500\/5:hover {
  background-color: rgb(168 85 247 / 0.05);
}

.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-600\/20:hover {
  background-color: rgb(147 51 234 / 0.2);
}

.hover\:bg-purple-600\/70:hover {
  background-color: rgb(147 51 234 / 0.7);
}

.hover\:bg-purple-600\/90:hover {
  background-color: rgb(147 51 234 / 0.9);
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-900\/30:hover {
  background-color: rgb(88 28 135 / 0.3);
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500\/50:hover {
  background-color: rgb(239 68 68 / 0.5);
}

.hover\:bg-red-600\/70:hover {
  background-color: rgb(220 38 38 / 0.7);
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700\/80:hover {
  background-color: rgb(185 28 28 / 0.8);
}

.hover\:bg-red-800\/50:hover {
  background-color: rgb(153 27 27 / 0.5);
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}

.hover\:bg-white\/5:hover {
  background-color: rgb(255 255 255 / 0.05);
}

.hover\:bg-white\/90:hover {
  background-color: rgb(255 255 255 / 0.9);
}

.hover\:bg-white\/\[0\.12\]:hover {
  background-color: rgb(255 255 255 / 0.12);
}

.hover\:bg-yellow-500\/30:hover {
  background-color: rgb(234 179 8 / 0.3);
}

.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-700\/80:hover {
  background-color: rgb(161 98 7 / 0.8);
}

.hover\:from-purple-700:hover {
  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-indigo-700:hover {
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}

.hover\:to-pink-700:hover {
  --tw-gradient-to: #be185d var(--tw-gradient-to-position);
}

.hover\:text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-300:hover {
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-400:hover {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-500:hover {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-800:hover {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-900:hover {
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-200:hover {
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-300:hover {
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-400:hover {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.hover\:text-red-300:hover {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.hover\:text-red-400:hover {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:text-white\/70:hover {
  color: rgb(255 255 255 / 0.7);
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-\[0_0_10px_rgba\(139\2c 92\2c 246\2c 0\.15\)\]:hover {
  --tw-shadow: 0 0 10px rgba(139,92,246,0.15);
  --tw-shadow-colored: 0 0 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_0_10px_rgba\(139\2c 92\2c 246\2c 0\.3\)\]:hover {
  --tw-shadow: 0 0 10px rgba(139,92,246,0.3);
  --tw-shadow-colored: 0 0 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_2px_12px_rgba\(139\2c 92\2c 246\2c 0\.15\)\]:hover {
  --tw-shadow: 0 2px 12px rgba(139,92,246,0.15);
  --tw-shadow-colored: 0 2px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_2px_12px_rgba\(139\2c 92\2c 246\2c 0\.2\)\]:hover {
  --tw-shadow: 0 2px 12px rgba(139,92,246,0.2);
  --tw-shadow-colored: 0 2px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_4px_20px_rgba\(139\2c 92\2c 246\2c 0\.35\)\]:hover {
  --tw-shadow: 0 4px 20px rgba(139,92,246,0.35);
  --tw-shadow-colored: 0 4px 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-black\/20:hover {
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-fuchsia-500\/10:hover {
  --tw-shadow-color: rgb(217 70 239 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-indigo-500\/10:hover {
  --tw-shadow-color: rgb(99 102 241 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-purple-500\/10:hover {
  --tw-shadow-color: rgb(168 85 247 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-purple-500\/40:hover {
  --tw-shadow-color: rgb(168 85 247 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-purple-700\/40:hover {
  --tw-shadow-color: rgb(126 34 206 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-violet-500\/10:hover {
  --tw-shadow-color: rgb(139 92 246 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:file\:bg-blue-100::file-selector-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:border-indigo-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}

.focus\:bg-purple-700\/50:focus {
  background-color: rgb(126 34 206 / 0.5);
}

.focus\:text-white:focus {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-inset:focus {
  --tw-ring-inset: inset;
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-500\/50:focus {
  --tw-ring-color: rgb(168 85 247 / 0.5);
}

.focus\:ring-purple-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-1:focus {
  --tw-ring-offset-width: 1px;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-800:focus {
  --tw-ring-offset-color: #1f2937;
}

.focus\:ring-offset-gray-900:focus {
  --tw-ring-offset-color: #111827;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:bg-blue-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:hover\:bg-blue-600:hover:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.disabled\:hover\:bg-purple-600:hover:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:block {
  display: block;
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:whitespace-normal {
  white-space: normal;
}

.group:hover .group-hover\:text-indigo-400 {
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-60 {
  opacity: 0.6;
}

.group:hover .group-hover\:shadow-indigo-500\/10 {
  --tw-shadow-color: rgb(99 102 241 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.group:hover .group-hover\:shadow-pink-500\/10 {
  --tw-shadow-color: rgb(236 72 153 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.group:hover .group-hover\:shadow-purple-500\/10 {
  --tw-shadow-color: rgb(168 85 247 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.peer:checked ~ .peer-checked\:bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:focus ~ .peer-focus\:ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus ~ .peer-focus\:ring-purple-500\/50 {
  --tw-ring-color: rgb(168 85 247 / 0.5);
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

@media (min-width: 640px) {

  .sm\:right-4 {
    right: 1rem;
  }

  .sm\:top-20 {
    top: 5rem;
  }

  .sm\:top-4 {
    top: 1rem;
  }

  .sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-1 {
    margin-bottom: 0.25rem;
  }

  .sm\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .sm\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .sm\:mb-4 {
    margin-bottom: 1rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:ml-3 {
    margin-left: 0.75rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:mr-0 {
    margin-right: 0px;
  }

  .sm\:mr-3 {
    margin-right: 0.75rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-2 {
    margin-top: 0.5rem;
  }

  .sm\:mt-3 {
    margin-top: 0.75rem;
  }

  .sm\:mt-4 {
    margin-top: 1rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:aspect-\[9\/16\] {
    aspect-ratio: 9/16;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-12 {
    height: 3rem;
  }

  .sm\:h-4 {
    height: 1rem;
  }

  .sm\:h-5 {
    height: 1.25rem;
  }

  .sm\:h-6 {
    height: 1.5rem;
  }

  .sm\:h-8 {
    height: 2rem;
  }

  .sm\:max-h-32 {
    max-height: 8rem;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-12 {
    width: 3rem;
  }

  .sm\:w-24 {
    width: 6rem;
  }

  .sm\:w-4 {
    width: 1rem;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-5 {
    width: 1.25rem;
  }

  .sm\:w-6 {
    width: 1.5rem;
  }

  .sm\:w-8 {
    width: 2rem;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:min-w-\[120px\] {
    min-width: 120px;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:max-w-\[425px\] {
    max-width: 425px;
  }

  .sm\:max-w-\[90\%\] {
    max-width: 90%;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-end {
    align-items: flex-end;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:p-2 {
    padding: 0.5rem;
  }

  .sm\:p-4 {
    padding: 1rem;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .sm\:py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .sm\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:pb-4 {
    padding-bottom: 1rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:align-middle {
    vertical-align: middle;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 768px) {

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:ml-2 {
    margin-left: 0.5rem;
  }

  .md\:ml-3 {
    margin-left: 0.75rem;
  }

  .md\:ml-4 {
    margin-left: 1rem;
  }

  .md\:ml-8 {
    margin-left: 2rem;
  }

  .md\:mr-2 {
    margin-right: 0.5rem;
  }

  .md\:mr-8 {
    margin-right: 2rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-20 {
    height: 5rem;
  }

  .md\:h-5 {
    height: 1.25rem;
  }

  .md\:h-6 {
    height: 1.5rem;
  }

  .md\:h-8 {
    height: 2rem;
  }

  .md\:h-9 {
    height: 2.25rem;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-16 {
    width: 4rem;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-5 {
    width: 1.25rem;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:w-64 {
    width: 16rem;
  }

  .md\:w-8 {
    width: 2rem;
  }

  .md\:w-9 {
    width: 2.25rem;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:max-w-4xl {
    max-width: 56rem;
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:overflow-visible {
    overflow: visible;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:text-right {
    text-align: right;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {

  .lg\:relative {
    position: relative;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:ml-20 {
    margin-left: 5rem;
  }

  .lg\:ml-64 {
    margin-left: 16rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-64 {
    width: 16rem;
  }

  .lg\:max-w-\[420px\] {
    max-width: 420px;
  }

  .lg\:max-w-xs {
    max-width: 20rem;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-9 {
    grid-template-columns: repeat(9, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-14 {
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .lg\:pl-6 {
    padding-left: 1.5rem;
  }

  .lg\:pr-12 {
    padding-right: 3rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 1280px) {

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (prefers-color-scheme: dark) {

  .dark\:divide-gray-700 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-divide-opacity, 1));
  }

  .dark\:border-blue-400 {
    --tw-border-opacity: 1;
    border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
  }

  .dark\:border-blue-500 {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
  }

  .dark\:border-blue-700 {
    --tw-border-opacity: 1;
    border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
  }

  .dark\:border-blue-800 {
    --tw-border-opacity: 1;
    border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-500 {
    --tw-border-opacity: 1;
    border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-600 {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
  }

  .dark\:border-gray-700 {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
  }

  .dark\:border-green-800 {
    --tw-border-opacity: 1;
    border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
  }

  .dark\:border-indigo-500 {
    --tw-border-opacity: 1;
    border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
  }

  .dark\:border-indigo-800 {
    --tw-border-opacity: 1;
    border-color: rgb(55 48 163 / var(--tw-border-opacity, 1));
  }

  .dark\:border-red-700 {
    --tw-border-opacity: 1;
    border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
  }

  .dark\:border-red-800 {
    --tw-border-opacity: 1;
    border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
  }

  .dark\:border-yellow-800 {
    --tw-border-opacity: 1;
    border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));
  }

  .dark\:bg-blue-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-blue-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-blue-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-blue-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-blue-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-blue-900\/20 {
    background-color: rgb(30 58 138 / 0.2);
  }

  .dark\:bg-blue-900\/30 {
    background-color: rgb(30 58 138 / 0.3);
  }

  .dark\:bg-blue-900\/50 {
    background-color: rgb(30 58 138 / 0.5);
  }

  .dark\:bg-gray-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-700\/50 {
    background-color: rgb(55 65 81 / 0.5);
  }

  .dark\:bg-gray-750 {
    --tw-bg-opacity: 1;
    background-color: rgb(45 55 72 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-gray-900\/50 {
    background-color: rgb(17 24 39 / 0.5);
  }

  .dark\:bg-green-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-green-900\/20 {
    background-color: rgb(20 83 45 / 0.2);
  }

  .dark\:bg-green-900\/30 {
    background-color: rgb(20 83 45 / 0.3);
  }

  .dark\:bg-indigo-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(49 46 129 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-indigo-900\/10 {
    background-color: rgb(49 46 129 / 0.1);
  }

  .dark\:bg-indigo-900\/20 {
    background-color: rgb(49 46 129 / 0.2);
  }

  .dark\:bg-indigo-900\/30 {
    background-color: rgb(49 46 129 / 0.3);
  }

  .dark\:bg-purple-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-purple-900\/30 {
    background-color: rgb(88 28 135 / 0.3);
  }

  .dark\:bg-red-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-red-900\/20 {
    background-color: rgb(127 29 29 / 0.2);
  }

  .dark\:bg-red-900\/30 {
    background-color: rgb(127 29 29 / 0.3);
  }

  .dark\:bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-yellow-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
  }

  .dark\:bg-yellow-900\/20 {
    background-color: rgb(113 63 18 / 0.2);
  }

  .dark\:bg-yellow-900\/30 {
    background-color: rgb(113 63 18 / 0.3);
  }

  .dark\:text-blue-200 {
    --tw-text-opacity: 1;
    color: rgb(191 219 254 / var(--tw-text-opacity, 1));
  }

  .dark\:text-blue-300 {
    --tw-text-opacity: 1;
    color: rgb(147 197 253 / var(--tw-text-opacity, 1));
  }

  .dark\:text-blue-400 {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-100 {
    --tw-text-opacity: 1;
    color: rgb(243 244 246 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-200 {
    --tw-text-opacity: 1;
    color: rgb(229 231 235 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
  }

  .dark\:text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity, 1));
  }

  .dark\:text-green-200 {
    --tw-text-opacity: 1;
    color: rgb(187 247 208 / var(--tw-text-opacity, 1));
  }

  .dark\:text-green-300 {
    --tw-text-opacity: 1;
    color: rgb(134 239 172 / var(--tw-text-opacity, 1));
  }

  .dark\:text-green-400 {
    --tw-text-opacity: 1;
    color: rgb(74 222 128 / var(--tw-text-opacity, 1));
  }

  .dark\:text-indigo-200 {
    --tw-text-opacity: 1;
    color: rgb(199 210 254 / var(--tw-text-opacity, 1));
  }

  .dark\:text-indigo-300 {
    --tw-text-opacity: 1;
    color: rgb(165 180 252 / var(--tw-text-opacity, 1));
  }

  .dark\:text-indigo-400 {
    --tw-text-opacity: 1;
    color: rgb(129 140 248 / var(--tw-text-opacity, 1));
  }

  .dark\:text-purple-200 {
    --tw-text-opacity: 1;
    color: rgb(233 213 255 / var(--tw-text-opacity, 1));
  }

  .dark\:text-purple-300 {
    --tw-text-opacity: 1;
    color: rgb(216 180 254 / var(--tw-text-opacity, 1));
  }

  .dark\:text-purple-400 {
    --tw-text-opacity: 1;
    color: rgb(192 132 252 / var(--tw-text-opacity, 1));
  }

  .dark\:text-red-200 {
    --tw-text-opacity: 1;
    color: rgb(254 202 202 / var(--tw-text-opacity, 1));
  }

  .dark\:text-red-300 {
    --tw-text-opacity: 1;
    color: rgb(252 165 165 / var(--tw-text-opacity, 1));
  }

  .dark\:text-red-400 {
    --tw-text-opacity: 1;
    color: rgb(248 113 113 / var(--tw-text-opacity, 1));
  }

  .dark\:text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
  }

  .dark\:text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .dark\:text-yellow-200 {
    --tw-text-opacity: 1;
    color: rgb(254 240 138 / var(--tw-text-opacity, 1));
  }

  .dark\:text-yellow-300 {
    --tw-text-opacity: 1;
    color: rgb(253 224 71 / var(--tw-text-opacity, 1));
  }

  .dark\:text-yellow-400 {
    --tw-text-opacity: 1;
    color: rgb(250 204 21 / var(--tw-text-opacity, 1));
  }

  .dark\:placeholder-gray-400::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
  }

  .dark\:placeholder-gray-400::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
  }

  .dark\:ring-blue-400\/50 {
    --tw-ring-color: rgb(96 165 250 / 0.5);
  }

  .dark\:ring-gray-600 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity, 1));
  }

  .dark\:ring-offset-gray-800 {
    --tw-ring-offset-color: #1f2937;
  }

  .dark\:file\:bg-blue-900\/30::file-selector-button {
    background-color: rgb(30 58 138 / 0.3);
  }

  .dark\:file\:text-blue-400::file-selector-button {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:border-gray-500:hover {
    --tw-border-opacity: 1;
    border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
  }

  .dark\:hover\:border-gray-600:hover {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
  }

  .dark\:hover\:bg-blue-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-blue-900\/10:hover {
    background-color: rgb(30 58 138 / 0.1);
  }

  .dark\:hover\:bg-blue-900\/50:hover {
    background-color: rgb(30 58 138 / 0.5);
  }

  .dark\:hover\:bg-gray-500:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-gray-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-gray-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-gray-700\/50:hover {
    background-color: rgb(55 65 81 / 0.5);
  }

  .dark\:hover\:bg-gray-750:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(45 55 72 / var(--tw-bg-opacity, 1));
  }

  .dark\:hover\:bg-red-900\/10:hover {
    background-color: rgb(127 29 29 / 0.1);
  }

  .dark\:hover\:bg-red-900\/20:hover {
    background-color: rgb(127 29 29 / 0.2);
  }

  .dark\:hover\:bg-red-900\/50:hover {
    background-color: rgb(127 29 29 / 0.5);
  }

  .dark\:hover\:text-blue-300:hover {
    --tw-text-opacity: 1;
    color: rgb(147 197 253 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-blue-400:hover {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-gray-300:hover {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-gray-400:hover {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-green-300:hover {
    --tw-text-opacity: 1;
    color: rgb(134 239 172 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-indigo-300:hover {
    --tw-text-opacity: 1;
    color: rgb(165 180 252 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-red-300:hover {
    --tw-text-opacity: 1;
    color: rgb(252 165 165 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-red-400:hover {
    --tw-text-opacity: 1;
    color: rgb(248 113 113 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .dark\:hover\:file\:bg-blue-900\/40::file-selector-button:hover {
    background-color: rgb(30 58 138 / 0.4);
  }

  .dark\:focus\:ring-offset-gray-800:focus {
    --tw-ring-offset-color: #1f2937;
  }
}
