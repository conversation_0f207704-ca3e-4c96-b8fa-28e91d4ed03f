/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CDynamicImports.jsx%22%2C%22ids%22%3A%5B%22ConditionalNavbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cproviders%5C%5CAppProviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CDynamicImports.jsx%22%2C%22ids%22%3A%5B%22ConditionalNavbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cproviders%5C%5CAppProviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/DynamicImports.jsx */ \"(ssr)/./app/components/DynamicImports.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/i18n/LanguageContext.jsx */ \"(ssr)/./app/i18n/LanguageContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers/SessionProvider.jsx */ \"(ssr)/./app/providers/SessionProvider.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./providers/AppProviders.tsx */ \"(ssr)/./providers/AppProviders.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CDynamicImports.jsx%22%2C%22ids%22%3A%5B%22ConditionalNavbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cproviders%5C%5CAppProviders.tsx%22%2C%22ids%22%3A%5B%22AppProviders%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/DynamicImports.jsx":
/*!*******************************************!*\
  !*** ./app/components/DynamicImports.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConditionalNavbar: () => (/* binding */ ConditionalNavbar),\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   PerformanceOptimizer: () => (/* binding */ PerformanceOptimizer),\n/* harmony export */   WebVitalsInit: () => (/* binding */ WebVitalsInit),\n/* harmony export */   \"default\": () => (/* binding */ DynamicImportsPlaceholder)\n/* harmony export */ });\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,PerformanceOptimizer,ConditionalNavbar,WebVitalsInit,default auto */ \n// Dynamically import components with no SSR and export them\nconst ErrorBoundary = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(/*! ./ErrorBoundary */ \"(ssr)/./app/components/ErrorBoundary.jsx\");\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\components\\\\DynamicImports.jsx -> \" + \"./ErrorBoundary\"\n        ]\n    },\n    ssr: false\n});\nconst PerformanceOptimizer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\components\\\\DynamicImports.jsx -> \" + \"./PerformanceOptimizer\"\n        ]\n    },\n    ssr: false\n});\nconst ConditionalNavbar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\components\\\\DynamicImports.jsx -> \" + \"./ConditionalNavbar\"\n        ]\n    },\n    ssr: false\n});\nconst WebVitalsInit = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\components\\\\DynamicImports.jsx -> \" + \"./WebVitalsInit\"\n        ]\n    },\n    ssr: false\n});\n// This component doesn't need to render anything itself.\n// We just use it as a place to perform the dynamic imports within a client context.\nfunction DynamicImportsPlaceholder() {\n    return null; // Or <></>\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9EeW5hbWljSW1wb3J0cy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O2dJQUVtQztBQUVuQyw0REFBNEQ7QUFDckQsTUFBTUMsZ0JBQWdCRCx3REFBT0E7Ozs7Ozs7O0lBQW9DRSxLQUFLO0dBQVM7QUFDL0UsTUFBTUMsdUJBQXVCSCx3REFBT0E7Ozs7Ozs7O0lBQTJDRSxLQUFLO0dBQVM7QUFDN0YsTUFBTUUsb0JBQW9CSix3REFBT0E7Ozs7Ozs7O0lBQXdDRSxLQUFLO0dBQVM7QUFDdkYsTUFBTUcsZ0JBQWdCTCx3REFBT0E7Ozs7Ozs7O0lBQW9DRSxLQUFLO0dBQVM7QUFFdEYseURBQXlEO0FBQ3pELG9GQUFvRjtBQUNyRSxTQUFTSTtJQUN0QixPQUFPLE1BQU0sV0FBVztBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NhbGxzYXZlcjQvLi9hcHAvY29tcG9uZW50cy9EeW5hbWljSW1wb3J0cy5qc3g/MGUwYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7IC8vIE1hcmsgdGhpcyBhcyBhIENsaWVudCBDb21wb25lbnRcclxuXHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG4vLyBEeW5hbWljYWxseSBpbXBvcnQgY29tcG9uZW50cyB3aXRoIG5vIFNTUiBhbmQgZXhwb3J0IHRoZW1cclxuZXhwb3J0IGNvbnN0IEVycm9yQm91bmRhcnkgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi9FcnJvckJvdW5kYXJ5JyksIHsgc3NyOiBmYWxzZSB9KTtcclxuZXhwb3J0IGNvbnN0IFBlcmZvcm1hbmNlT3B0aW1pemVyID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJy4vUGVyZm9ybWFuY2VPcHRpbWl6ZXInKSwgeyBzc3I6IGZhbHNlIH0pO1xyXG5leHBvcnQgY29uc3QgQ29uZGl0aW9uYWxOYXZiYXIgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi9Db25kaXRpb25hbE5hdmJhcicpLCB7IHNzcjogZmFsc2UgfSk7XHJcbmV4cG9ydCBjb25zdCBXZWJWaXRhbHNJbml0ID0gZHluYW1pYygoKSA9PiBpbXBvcnQoJy4vV2ViVml0YWxzSW5pdCcpLCB7IHNzcjogZmFsc2UgfSk7XHJcblxyXG4vLyBUaGlzIGNvbXBvbmVudCBkb2Vzbid0IG5lZWQgdG8gcmVuZGVyIGFueXRoaW5nIGl0c2VsZi5cclxuLy8gV2UganVzdCB1c2UgaXQgYXMgYSBwbGFjZSB0byBwZXJmb3JtIHRoZSBkeW5hbWljIGltcG9ydHMgd2l0aGluIGEgY2xpZW50IGNvbnRleHQuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIER5bmFtaWNJbXBvcnRzUGxhY2Vob2xkZXIoKSB7XHJcbiAgcmV0dXJuIG51bGw7IC8vIE9yIDw+PC8+XHJcbn1cclxuIl0sIm5hbWVzIjpbImR5bmFtaWMiLCJFcnJvckJvdW5kYXJ5Iiwic3NyIiwiUGVyZm9ybWFuY2VPcHRpbWl6ZXIiLCJDb25kaXRpb25hbE5hdmJhciIsIldlYlZpdGFsc0luaXQiLCJEeW5hbWljSW1wb3J0c1BsYWNlaG9sZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/DynamicImports.jsx\n");

/***/ }),

/***/ "(ssr)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _locales_en_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./locales/en.json */ \"(ssr)/./app/i18n/locales/en.json\");\n/* harmony import */ var _locales_de_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./locales/de.json */ \"(ssr)/./app/i18n/locales/de.json\");\n/* harmony import */ var _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./locales/ar.json */ \"(ssr)/./app/i18n/locales/ar.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\n\n\n// Language configurations\nconst LANGUAGES = {\n    en: {\n        code: \"en\",\n        name: \"English\",\n        dir: \"ltr\",\n        translations: _locales_en_json__WEBPACK_IMPORTED_MODULE_2__,\n        flag: \"\\uD83C\\uDDEC\\uD83C\\uDDE7\"\n    },\n    de: {\n        code: \"de\",\n        name: \"Deutsch\",\n        dir: \"ltr\",\n        translations: _locales_de_json__WEBPACK_IMPORTED_MODULE_3__,\n        flag: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\"\n    },\n    ar: {\n        code: \"ar\",\n        name: \"العربية\",\n        dir: \"rtl\",\n        translations: _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__,\n        flag: \"\\uD83C\\uDDE6\\uD83C\\uDDEA\"\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LANGUAGES.en);\n    // Detect user's language\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Function to detect language from navigator or localStorage\n        const detectLanguage = ()=>{\n            // Check if there's a stored preference\n            const storedLang = localStorage.getItem(\"preferred-language\");\n            if (storedLang && LANGUAGES[storedLang]) {\n                return LANGUAGES[storedLang];\n            }\n            // Detect browser language\n            const browserLang = navigator.language.split(\"-\")[0].toLowerCase();\n            if (LANGUAGES[browserLang]) {\n                return LANGUAGES[browserLang];\n            }\n            // Default to English\n            return LANGUAGES.en;\n        };\n        // Set the detected language\n        setLanguage(detectLanguage());\n        // Update document direction for RTL support\n        document.documentElement.dir = detectLanguage().dir;\n        if (detectLanguage().dir === \"rtl\") {\n            document.documentElement.classList.add(\"rtl\");\n        } else {\n            document.documentElement.classList.remove(\"rtl\");\n        }\n    }, []);\n    // Update document direction when language changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        document.documentElement.dir = language.dir;\n        if (language.dir === \"rtl\") {\n            document.documentElement.classList.add(\"rtl\");\n        } else {\n            document.documentElement.classList.remove(\"rtl\");\n        }\n        // Store the preference\n        localStorage.setItem(\"preferred-language\", language.code);\n    }, [\n        language\n    ]);\n    // Function to change language\n    const changeLanguage = (langCode)=>{\n        if (LANGUAGES[langCode]) {\n            setLanguage(LANGUAGES[langCode]);\n        }\n    };\n    // Helper function to get a translation by key path\n    const t = (keyPath)=>{\n        const keys = keyPath.split(\".\");\n        let value = language.translations;\n        for (const key of keys){\n            if (value && value[key]) {\n                value = value[key];\n            } else {\n                return keyPath; // Fallback to key if translation not found\n            }\n        }\n        return value;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            changeLanguage,\n            t,\n            languages: LANGUAGES,\n            isRTL: language.dir === \"rtl\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\i18n\\\\LanguageContext.jsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the language context\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/i18n/LanguageContext.jsx\n");

/***/ }),

/***/ "(ssr)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* __next_internal_client_entry_do_not_use__ SessionProvider,useSession,default auto */ \n\n // Import the function\n// Create a context for the session\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction SessionProvider({ children }) {\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const lastEventRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        type: null,\n        timestamp: 0\n    });\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let mounted = true;\n        // Get the initial session\n        const getInitialSession = async ()=>{\n            try {\n                // Check for demo user in localStorage first\n                const demoUser = localStorage.getItem(\"callsaver_demo_user\");\n                if (demoUser) {\n                    console.log(\"Demo user found in localStorage\");\n                    const parsedUser = JSON.parse(demoUser);\n                    // Create a mock session for the demo user\n                    if (mounted) {\n                        setSession({\n                            user: {\n                                id: parsedUser.id,\n                                email: parsedUser.email,\n                                user_metadata: {\n                                    name: parsedUser.name,\n                                    role: parsedUser.role\n                                }\n                            },\n                            expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now\n                        });\n                        setLoading(false);\n                    }\n                    return;\n                }\n                // Get the client instance\n                const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                if (!supabase) {\n                    console.error(\"Failed to get Supabase client in SessionProvider\");\n                    if (mounted) setLoading(false);\n                    return; // Don't proceed without a client\n                }\n                // Try to get the session from Supabase\n                const { data, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting initial session:\", error);\n                    if (mounted) setSession(null);\n                } else {\n                    console.log(\"Initial session check:\", data.session ? \"Session found\" : \"No session\");\n                    if (mounted) setSession(data.session);\n                }\n            } catch (err) {\n                console.error(\"Unexpected error getting session:\", err);\n                if (mounted) setSession(null);\n            } finally{\n                if (mounted) setLoading(false);\n            }\n        };\n        // Debounced session update function\n        const updateSession = (newSession, eventType)=>{\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                if (mounted) {\n                    setSession(newSession);\n                    setLoading(false);\n                    lastEventRef.current = {\n                        type: eventType,\n                        timestamp: Date.now()\n                    };\n                }\n            }, 100); // 100ms debounce\n        };\n        // Get the client instance for the listener\n        const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n        if (!supabase) {\n            console.error(\"Failed to get Supabase client for auth listener\");\n            setLoading(false); // Ensure loading state is updated\n            return;\n        }\n        // Set up auth state listener with improved debouncing\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, newSession)=>{\n            // Skip if it's the same event type within 1 second\n            const now = Date.now();\n            const timeSinceLastEvent = now - lastEventRef.current.timestamp;\n            if (event === lastEventRef.current.type && timeSinceLastEvent < 1000) {\n                console.log(\"Skipping duplicate auth event:\", event);\n                return;\n            }\n            console.log(\"Auth state changed:\", event);\n            // Handle specific auth events\n            switch(event){\n                case \"SIGNED_IN\":\n                    updateSession(newSession, event);\n                    break;\n                case \"SIGNED_OUT\":\n                    updateSession(null, event);\n                    break;\n                case \"TOKEN_REFRESHED\":\n                case \"USER_UPDATED\":\n                    if (newSession) {\n                        updateSession(newSession, event);\n                    }\n                    break;\n                default:\n                    // For other events, only update if there's a meaningful change\n                    if (newSession?.user?.id !== session?.user?.id) {\n                        updateSession(newSession, event);\n                    }\n                    break;\n            }\n        });\n        getInitialSession();\n        // Clean up\n        return ()=>{\n            mounted = false;\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            subscription?.unsubscribe();\n        };\n    }, []);\n    const value = {\n        session,\n        loading,\n        isAuthenticated: !!session,\n        user: session?.user || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\providers\\\\SessionProvider.jsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use the session context\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error(\"useSession must be used within a SessionProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SessionProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers/SessionProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./app/utils/supabaseClient.js":
/*!*************************************!*\
  !*** ./app/utils/supabaseClient.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ getSupabaseClient,default auto */ \n// Global variable to hold the singleton instance\nlet clientInstance = null;\n// Flag to track initialization in progress\nlet initializationInProgress = false;\n// Flag to indicate if an initialization attempt has been made\nlet initializationAttempted = false;\n// Create a simple mock client that won't throw errors\nconst createMockClient = (reason)=>{\n    console.warn(`Creating mock Supabase client: ${reason}`);\n    return {\n        auth: {\n            getSession: async ()=>({\n                    data: {\n                        session: null\n                    },\n                    error: null\n                }),\n            signInWithPassword: async ()=>({\n                    data: null,\n                    error: new Error(`Supabase client unavailable: ${reason}`)\n                }),\n            signOut: async ()=>({\n                    error: null\n                }),\n            onAuthStateChange: ()=>({\n                    data: {\n                        subscription: {\n                            unsubscribe: ()=>{}\n                        }\n                    }\n                })\n        },\n        // Add minimal implementations for other commonly used methods\n        from: ()=>({\n                select: ()=>({\n                        data: [],\n                        error: null\n                    }),\n                insert: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                update: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                delete: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    })\n            })\n    };\n};\n// Function to create or return the singleton Supabase client instance\nconst getSupabaseClient = ()=>{\n    // Return existing instance if already created and valid\n    if (clientInstance && clientInstance.auth && typeof clientInstance.auth.getSession === \"function\") {\n        return clientInstance;\n    }\n    // If initialization is already in progress, wait for it to complete\n    if (initializationInProgress) {\n        throw new Error(\"Supabase client initialization in progress. Please retry your operation.\");\n    }\n    // Ensure this runs only on the client\n    if (true) {\n        throw new Error(\"Supabase client can only be initialized in browser environment\");\n    }\n    // Set flag to indicate we're attempting initialization\n    initializationInProgress = true;\n    initializationAttempted = true;\n    try {\n        const supabaseUrl = \"https://tlylzhcdynxbqxiihipe.supabase.co\";\n        const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRseWx6aGNkeW54YnF4aWloaXBlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyMDU2OTIsImV4cCI6MjA1ODc4MTY5Mn0.oWZ7PSgHRyU9iu2hqc5GEwxC0g8NveB-5ZhqRrlLn8Y\";\n        if (!supabaseUrl || !supabaseAnonKey) {\n            initializationInProgress = false;\n            throw new Error(\"Supabase credentials are missing in environment variables\");\n        }\n        // Create the actual client instance\n        const newClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                flowType: \"pkce\",\n                detectSessionInUrl: true,\n                persistSession: true\n            }\n        });\n        // Validate the created client\n        if (!newClient || !newClient.auth || typeof newClient.auth.getSession !== \"function\") {\n            throw new Error(\"Created client is invalid or missing auth methods\");\n        }\n        // Set up auth state change listener for better debugging (optional here)\n        newClient.auth.onAuthStateChange((event, session)=>{\n            console.log(\"[getSupabaseClient] Auth state changed:\", event, session ? \"Session exists\" : \"No session\");\n            // Example: Update local storage flag on sign-in/sign-out\n            // Ensure window check wraps localStorage access\n            if (false) {}\n        });\n        // Store the instance globally\n        clientInstance = newClient;\n        initializationInProgress = false;\n        console.log(\"Supabase client initialized successfully.\");\n        return clientInstance;\n    } catch (error) {\n        console.error(\"Failed to initialize Supabase client:\", error);\n        clientInstance = null; // Ensure instance is null on error\n        initializationInProgress = false;\n        throw error; // Throw the error instead of returning a mock client\n    }\n};\n// Default export the function for easy import\n// Note: Files importing this will need to change from `import supabaseClient from ...`\n// to `import getSupabaseClient from ...` and call `getSupabaseClient()`\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getSupabaseClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/utils/supabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./lib/apiClient.ts":
/*!**************************!*\
  !*** ./lib/apiClient.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"/api\",\n    withCredentials: true,\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"X-Requested-With\": \"XMLHttpRequest\"\n    }\n});\n// Request Interceptor - Add auth token from .env if available\napiClient.interceptors.request.use((config)=>{\n    // Get API token from environment if available\n    const apiToken = process.env.NEXT_PUBLIC_API_TOKEN;\n    if (apiToken && config.headers) {\n        config.headers.Authorization = `Bearer ${apiToken}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Global error handling with toast notifications\n// Response Interceptor for global error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    const { response } = error;\n    if (response) {\n        // Handle specific status codes globally\n        if (response.status === 401) {\n            // Unauthorized: User session expired or invalid\n            console.error(\"API Error 401: Unauthorized\");\n            // We'll handle this using the authStore later\n            // (imported directly here would create circular dependency)\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"Your session has expired. Please log in again.\");\n            // Redirect to login page after a short delay\n            setTimeout(()=>{\n                window.location.href = \"/login\";\n            }, 1500);\n        } else if (response.status === 403) {\n            // Forbidden: User does not have permission\n            console.error(\"API Error 403: Forbidden\", response.data);\n            // Get the error message from the response if available\n            const errorMessage = response.data?.message || \"You do not have permission to perform this action\";\n            // Show a permission denied message\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(errorMessage, {\n                duration: 5000,\n                icon: \"\\uD83D\\uDD12\"\n            });\n        } else if (response.status >= 500) {\n            // Server Error\n            console.error(`API Error ${response.status}: Server Error`, response.data);\n            // Show a generic server error message\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"An unexpected server error occurred. Please try again later.\", {\n                duration: 5000\n            });\n        }\n    } else if (error.request) {\n        // Request was made but no response received (network error)\n        console.error(\"API Error: No response received\", error.request);\n        // Show a network error message\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"Network error. Please check your internet connection.\", {\n            duration: 5000,\n            icon: \"\\uD83D\\uDCF6\"\n        });\n    } else {\n        // Something happened in setting up the request\n        console.error(\"API Error: Request setup error\", error.message);\n        // Show a generic error message\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"An error occurred while processing your request.\", {\n            duration: 5000\n        });\n    }\n    // Return the rejected promise so React Query/component can handle specific errors\n    return Promise.reject(error);\n});\n// Utility functions for common HTTP methods\nconst api = {\n    // GET request\n    get: async (url, params)=>{\n        const response = await apiClient.get(url, {\n            params\n        });\n        return response.data;\n    },\n    // POST request\n    post: async (url, data)=>{\n        const response = await apiClient.post(url, data);\n        return response.data;\n    },\n    // PUT request\n    put: async (url, data)=>{\n        const response = await apiClient.put(url, data);\n        return response.data;\n    },\n    // DELETE request\n    delete: async (url)=>{\n        const response = await apiClient.delete(url);\n        return response.data;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./providers/AppProviders.tsx":
/*!************************************!*\
  !*** ./providers/AppProviders.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProviders: () => (/* binding */ AppProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QueryProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryProvider */ \"(ssr)/./providers/QueryProvider.tsx\");\n/* harmony import */ var _WebSocketProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./WebSocketProvider */ \"(ssr)/./providers/WebSocketProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppProviders auto */ \n\n\n\n/**\r\n * AppProviders\r\n * Combines all application providers in the correct order\r\n */ function AppProviders({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QueryProvider__WEBPACK_IMPORTED_MODULE_2__.QueryProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WebSocketProvider__WEBPACK_IMPORTED_MODULE_3__.WebSocketProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\providers\\\\AppProviders.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\providers\\\\AppProviders.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvQXBwUHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUwQjtBQUNzQjtBQUNRO0FBTXhEOzs7Q0FHQyxHQUNNLFNBQVNHLGFBQWEsRUFBRUMsUUFBUSxFQUFxQjtJQUMxRCxxQkFDRSw4REFBQ0gseURBQWFBO2tCQUNaLDRFQUFDQyxpRUFBaUJBO3NCQUNmRTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NhbGxzYXZlcjQvLi9wcm92aWRlcnMvQXBwUHJvdmlkZXJzLnRzeD82NjNjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgUXVlcnlQcm92aWRlciB9IGZyb20gJy4vUXVlcnlQcm92aWRlcic7XHJcbmltcG9ydCB7IFdlYlNvY2tldFByb3ZpZGVyIH0gZnJvbSAnLi9XZWJTb2NrZXRQcm92aWRlcic7XHJcblxyXG5pbnRlcmZhY2UgQXBwUHJvdmlkZXJzUHJvcHMge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBcHBQcm92aWRlcnNcclxuICogQ29tYmluZXMgYWxsIGFwcGxpY2F0aW9uIHByb3ZpZGVycyBpbiB0aGUgY29ycmVjdCBvcmRlclxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIEFwcFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IEFwcFByb3ZpZGVyc1Byb3BzKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxRdWVyeVByb3ZpZGVyPlxyXG4gICAgICA8V2ViU29ja2V0UHJvdmlkZXI+XHJcbiAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICA8L1dlYlNvY2tldFByb3ZpZGVyPlxyXG4gICAgPC9RdWVyeVByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUXVlcnlQcm92aWRlciIsIldlYlNvY2tldFByb3ZpZGVyIiwiQXBwUHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./providers/AppProviders.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/QueryProvider.tsx":
/*!*************************************!*\
  !*** ./providers/QueryProvider.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider),\n/* harmony export */   queryClient: () => (/* binding */ queryClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* __next_internal_client_entry_do_not_use__ QueryProvider,queryClient auto */ \n\n\n\n// Create a client\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n    defaultOptions: {\n        queries: {\n            refetchOnWindowFocus: true,\n            refetchOnMount: true,\n            refetchOnReconnect: true,\n            retry: 1,\n            staleTime: 5 * 60 * 1000\n        }\n    },\n    queryCache: new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryCache({\n        onError: (error, query)=>{\n            // Log errors globally but don't show a toast for all errors\n            console.error(`Query Error:`, error, query);\n        }\n    }),\n    mutationCache: new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.MutationCache({\n        onError: (error, variables, context, mutation)=>{\n            // Log mutation errors globally\n            console.error(`Mutation Error:`, error, variables);\n        }\n    })\n});\n/**\r\n * React Query Provider \r\n * Wraps the application to provide React Query functionality\r\n */ function QueryProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_6__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\providers\\\\QueryProvider.tsx\",\n                lineNumber: 49,\n                columnNumber: 49\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n// Export the QueryClient to allow manual interaction with the cache\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./providers/WebSocketProvider.tsx":
/*!*****************************************!*\
  !*** ./providers/WebSocketProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketProvider: () => (/* binding */ WebSocketProvider),\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../stores/authStore */ \"(ssr)/./stores/authStore.ts\");\n/* harmony import */ var _stores_uiStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../stores/uiStore */ \"(ssr)/./stores/uiStore.ts\");\n/* harmony import */ var _QueryProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QueryProvider */ \"(ssr)/./providers/QueryProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ WebSocketProvider,useWebSocket auto */ \n\n\n\n\n// Create the context with default values\nconst WebSocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isConnected: false,\n    lastPong: null,\n    send: ()=>{}\n});\n/**\r\n * WebSocket Provider\r\n * Manages WebSocket connection and message handling\r\n */ function WebSocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastPong, setLastPong] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [reconnectAttempt, setReconnectAttempt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const isAuthenticated = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)((state)=>state.isAuthenticated);\n    const user = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)((state)=>state.user);\n    const incrementUnreadNotifications = (0,_stores_uiStore__WEBPACK_IMPORTED_MODULE_3__.useUIStore)((state)=>state.incrementUnreadNotificationsCount);\n    // Connection and reconnection logic\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only connect if user is authenticated\n        if (!isAuthenticated || !user) {\n            if (socket) {\n                socket.close();\n                setSocket(null);\n                setIsConnected(false);\n            }\n            return;\n        }\n        // WebSocket server URL from environment\n        const wsUrl = process.env.NEXT_PUBLIC_WS_URL || `${window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\"}//${window.location.host}/ws`;\n        // Initialize WebSocket\n        const ws = new WebSocket(`${wsUrl}?userId=${user.id}`);\n        // Set up WebSocket event handlers\n        ws.onopen = ()=>{\n            console.log(\"WebSocket connected\");\n            setIsConnected(true);\n            setReconnectAttempt(0); // Reset reconnect attempts\n            // Send an initial authentication message\n            ws.send(JSON.stringify({\n                type: \"auth\",\n                userId: user.id\n            }));\n        };\n        ws.onclose = (event)=>{\n            console.log(\"WebSocket disconnected:\", event.code, event.reason);\n            setIsConnected(false);\n            // Implement reconnection logic with exponential backoff\n            if (isAuthenticated) {\n                const backoffTime = Math.min(1000 * Math.pow(2, reconnectAttempt), 30000); // Max 30 seconds\n                console.log(`Reconnecting in ${backoffTime}ms (attempt ${reconnectAttempt + 1})`);\n                setTimeout(()=>{\n                    setReconnectAttempt((prev)=>prev + 1);\n                }, backoffTime);\n            }\n        };\n        ws.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n        };\n        ws.onmessage = (event)=>{\n            try {\n                const message = JSON.parse(event.data);\n                handleMessage(message);\n            } catch (error) {\n                console.error(\"Error parsing WebSocket message:\", error);\n            }\n        };\n        // Store the WebSocket instance\n        setSocket(ws);\n        // Clean up on unmount\n        return ()=>{\n            ws.close();\n        };\n    }, [\n        isAuthenticated,\n        user,\n        reconnectAttempt\n    ]);\n    // Handle different types of WebSocket messages\n    const handleMessage = (message)=>{\n        console.log(\"WebSocket message received:\", message);\n        // Handle different message types\n        switch(message.type){\n            case \"pong\":\n                setLastPong(new Date());\n                break;\n            case \"notification\":\n                // When a new notification arrives\n                incrementUnreadNotifications();\n                // Invalidate notifications query to refetch\n                _QueryProvider__WEBPACK_IMPORTED_MODULE_4__.queryClient.invalidateQueries({\n                    queryKey: [\n                        \"notifications\"\n                    ]\n                });\n                break;\n            case \"call_status_updated\":\n                // Invalidate call logs to refresh the list\n                _QueryProvider__WEBPACK_IMPORTED_MODULE_4__.queryClient.invalidateQueries({\n                    queryKey: [\n                        \"call-logs\"\n                    ]\n                });\n                break;\n            case \"credit_updated\":\n                // Update credit balance in UI\n                _QueryProvider__WEBPACK_IMPORTED_MODULE_4__.queryClient.invalidateQueries({\n                    queryKey: [\n                        \"credits\"\n                    ]\n                });\n                break;\n            case \"new_number_assigned\":\n                // Refresh phone numbers list\n                _QueryProvider__WEBPACK_IMPORTED_MODULE_4__.queryClient.invalidateQueries({\n                    queryKey: [\n                        \"numbers\"\n                    ]\n                });\n                break;\n            default:\n                console.log(\"Unhandled WebSocket message type:\", message.type);\n        }\n    };\n    // Function to send a message through the WebSocket\n    const send = (data)=>{\n        if (socket && isConnected) {\n            socket.send(JSON.stringify(data));\n        } else {\n            console.warn(\"Cannot send message: WebSocket is not connected\");\n        }\n    };\n    // Keep WebSocket alive with ping/pong (if server supports it)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isConnected) return;\n        const pingInterval = setInterval(()=>{\n            if (socket && socket.readyState === WebSocket.OPEN) {\n                send({\n                    type: \"ping\"\n                });\n            }\n        }, 30000); // Send ping every 30 seconds\n        return ()=>{\n            clearInterval(pingInterval);\n        };\n    }, [\n        socket,\n        isConnected\n    ]);\n    // Context value\n    const value = {\n        isConnected,\n        lastPong,\n        send\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebSocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\providers\\\\WebSocketProvider.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the WebSocket context\nconst useWebSocket = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WebSocketContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wcm92aWRlcnMvV2ViU29ja2V0UHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFOEU7QUFDM0I7QUFDSjtBQUNEO0FBUzlDLHlDQUF5QztBQUN6QyxNQUFNUSxpQ0FBbUJQLG9EQUFhQSxDQUF1QjtJQUMzRFEsYUFBYTtJQUNiQyxVQUFVO0lBQ1ZDLE1BQU0sS0FBTztBQUNmO0FBTUE7OztDQUdDLEdBQ00sU0FBU0Msa0JBQWtCLEVBQUVDLFFBQVEsRUFBMEI7SUFDcEUsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdYLCtDQUFRQSxDQUFtQjtJQUN2RCxNQUFNLENBQUNLLGFBQWFPLGVBQWUsR0FBR1osK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDTSxVQUFVTyxZQUFZLEdBQUdiLCtDQUFRQSxDQUFjO0lBQ3RELE1BQU0sQ0FBQ2Msa0JBQWtCQyxvQkFBb0IsR0FBR2YsK0NBQVFBLENBQUM7SUFFekQsTUFBTWdCLGtCQUFrQmYsK0RBQVlBLENBQUNnQixDQUFBQSxRQUFTQSxNQUFNRCxlQUFlO0lBQ25FLE1BQU1FLE9BQU9qQiwrREFBWUEsQ0FBQ2dCLENBQUFBLFFBQVNBLE1BQU1DLElBQUk7SUFDN0MsTUFBTUMsK0JBQStCakIsMkRBQVVBLENBQUNlLENBQUFBLFFBQVNBLE1BQU1HLGlDQUFpQztJQUVoRyxvQ0FBb0M7SUFDcENyQixnREFBU0EsQ0FBQztRQUNSLHdDQUF3QztRQUN4QyxJQUFJLENBQUNpQixtQkFBbUIsQ0FBQ0UsTUFBTTtZQUM3QixJQUFJUixRQUFRO2dCQUNWQSxPQUFPVyxLQUFLO2dCQUNaVixVQUFVO2dCQUNWQyxlQUFlO1lBQ2pCO1lBQ0E7UUFDRjtRQUVBLHdDQUF3QztRQUN4QyxNQUFNVSxRQUFRQyxRQUFRQyxHQUFHLENBQUNDLGtCQUFrQixJQUFJLENBQUMsRUFBRUMsT0FBT0MsUUFBUSxDQUFDQyxRQUFRLEtBQUssV0FBVyxTQUFTLE1BQU0sRUFBRSxFQUFFRixPQUFPQyxRQUFRLENBQUNFLElBQUksQ0FBQyxHQUFHLENBQUM7UUFFdkksdUJBQXVCO1FBQ3ZCLE1BQU1DLEtBQUssSUFBSUMsVUFBVSxDQUFDLEVBQUVULE1BQU0sUUFBUSxFQUFFSixLQUFLYyxFQUFFLENBQUMsQ0FBQztRQUVyRCxrQ0FBa0M7UUFDbENGLEdBQUdHLE1BQU0sR0FBRztZQUNWQyxRQUFRQyxHQUFHLENBQUM7WUFDWnZCLGVBQWU7WUFDZkcsb0JBQW9CLElBQUksMkJBQTJCO1lBRW5ELHlDQUF5QztZQUN6Q2UsR0FBR3ZCLElBQUksQ0FBQzZCLEtBQUtDLFNBQVMsQ0FBQztnQkFDckJDLE1BQU07Z0JBQ05DLFFBQVFyQixLQUFLYyxFQUFFO1lBQ2pCO1FBQ0Y7UUFFQUYsR0FBR1UsT0FBTyxHQUFHLENBQUNDO1lBQ1pQLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJNLE1BQU1DLElBQUksRUFBRUQsTUFBTUUsTUFBTTtZQUMvRC9CLGVBQWU7WUFFZix3REFBd0Q7WUFDeEQsSUFBSUksaUJBQWlCO2dCQUNuQixNQUFNNEIsY0FBY0MsS0FBS0MsR0FBRyxDQUFDLE9BQU9ELEtBQUtFLEdBQUcsQ0FBQyxHQUFHakMsbUJBQW1CLFFBQVEsaUJBQWlCO2dCQUM1Rm9CLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdCQUFnQixFQUFFUyxZQUFZLFlBQVksRUFBRTlCLG1CQUFtQixFQUFFLENBQUMsQ0FBQztnQkFFaEZrQyxXQUFXO29CQUNUakMsb0JBQW9Ca0MsQ0FBQUEsT0FBUUEsT0FBTztnQkFDckMsR0FBR0w7WUFDTDtRQUNGO1FBRUFkLEdBQUdvQixPQUFPLEdBQUcsQ0FBQ0M7WUFDWmpCLFFBQVFpQixLQUFLLENBQUMsb0JBQW9CQTtRQUNwQztRQUVBckIsR0FBR3NCLFNBQVMsR0FBRyxDQUFDWDtZQUNkLElBQUk7Z0JBQ0YsTUFBTVksVUFBVWpCLEtBQUtrQixLQUFLLENBQUNiLE1BQU1jLElBQUk7Z0JBQ3JDQyxjQUFjSDtZQUNoQixFQUFFLE9BQU9GLE9BQU87Z0JBQ2RqQixRQUFRaUIsS0FBSyxDQUFDLG9DQUFvQ0E7WUFDcEQ7UUFDRjtRQUVBLCtCQUErQjtRQUMvQnhDLFVBQVVtQjtRQUVWLHNCQUFzQjtRQUN0QixPQUFPO1lBQ0xBLEdBQUdULEtBQUs7UUFDVjtJQUNGLEdBQUc7UUFBQ0w7UUFBaUJFO1FBQU1KO0tBQWlCO0lBRTVDLCtDQUErQztJQUMvQyxNQUFNMEMsZ0JBQWdCLENBQUNIO1FBQ3JCbkIsUUFBUUMsR0FBRyxDQUFDLCtCQUErQmtCO1FBRTNDLGlDQUFpQztRQUNqQyxPQUFRQSxRQUFRZixJQUFJO1lBQ2xCLEtBQUs7Z0JBQ0h6QixZQUFZLElBQUk0QztnQkFDaEI7WUFFRixLQUFLO2dCQUNILGtDQUFrQztnQkFDbEN0QztnQkFDQSw0Q0FBNEM7Z0JBQzVDaEIsdURBQVdBLENBQUN1RCxpQkFBaUIsQ0FBQztvQkFBRUMsVUFBVTt3QkFBQztxQkFBZ0I7Z0JBQUM7Z0JBQzVEO1lBRUYsS0FBSztnQkFDSCwyQ0FBMkM7Z0JBQzNDeEQsdURBQVdBLENBQUN1RCxpQkFBaUIsQ0FBQztvQkFBRUMsVUFBVTt3QkFBQztxQkFBWTtnQkFBQztnQkFDeEQ7WUFFRixLQUFLO2dCQUNILDhCQUE4QjtnQkFDOUJ4RCx1REFBV0EsQ0FBQ3VELGlCQUFpQixDQUFDO29CQUFFQyxVQUFVO3dCQUFDO3FCQUFVO2dCQUFDO2dCQUN0RDtZQUVGLEtBQUs7Z0JBQ0gsNkJBQTZCO2dCQUM3QnhELHVEQUFXQSxDQUFDdUQsaUJBQWlCLENBQUM7b0JBQUVDLFVBQVU7d0JBQUM7cUJBQVU7Z0JBQUM7Z0JBQ3REO1lBRUY7Z0JBQ0V6QixRQUFRQyxHQUFHLENBQUMscUNBQXFDa0IsUUFBUWYsSUFBSTtRQUNqRTtJQUNGO0lBRUEsbURBQW1EO0lBQ25ELE1BQU0vQixPQUFPLENBQUNnRDtRQUNaLElBQUk3QyxVQUFVTCxhQUFhO1lBQ3pCSyxPQUFPSCxJQUFJLENBQUM2QixLQUFLQyxTQUFTLENBQUNrQjtRQUM3QixPQUFPO1lBQ0xyQixRQUFRMEIsSUFBSSxDQUFDO1FBQ2Y7SUFDRjtJQUVBLDhEQUE4RDtJQUM5RDdELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDTSxhQUFhO1FBRWxCLE1BQU13RCxlQUFlQyxZQUFZO1lBQy9CLElBQUlwRCxVQUFVQSxPQUFPcUQsVUFBVSxLQUFLaEMsVUFBVWlDLElBQUksRUFBRTtnQkFDbER6RCxLQUFLO29CQUFFK0IsTUFBTTtnQkFBTztZQUN0QjtRQUNGLEdBQUcsUUFBUSw2QkFBNkI7UUFFeEMsT0FBTztZQUNMMkIsY0FBY0o7UUFDaEI7SUFDRixHQUFHO1FBQUNuRDtRQUFRTDtLQUFZO0lBRXhCLGdCQUFnQjtJQUNoQixNQUFNNkQsUUFBUTtRQUNaN0Q7UUFDQUM7UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDSCxpQkFBaUIrRCxRQUFRO1FBQUNELE9BQU9BO2tCQUMvQnpEOzs7Ozs7QUFHUDtBQUVBLDJDQUEyQztBQUNwQyxNQUFNMkQsZUFBZSxJQUFNdEUsaURBQVVBLENBQUNNLGtCQUFrQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NhbGxzYXZlcjQvLi9wcm92aWRlcnMvV2ViU29ja2V0UHJvdmlkZXIudHN4PzlmNzYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlQXV0aFN0b3JlIH0gZnJvbSAnLi4vc3RvcmVzL2F1dGhTdG9yZSc7XHJcbmltcG9ydCB7IHVzZVVJU3RvcmUgfSBmcm9tICcuLi9zdG9yZXMvdWlTdG9yZSc7XHJcbmltcG9ydCB7IHF1ZXJ5Q2xpZW50IH0gZnJvbSAnLi9RdWVyeVByb3ZpZGVyJztcclxuXHJcbi8vIERlZmluZSB0aGUgV2ViU29ja2V0IGNvbnRleHQgdHlwZVxyXG5pbnRlcmZhY2UgV2ViU29ja2V0Q29udGV4dFR5cGUge1xyXG4gIGlzQ29ubmVjdGVkOiBib29sZWFuO1xyXG4gIGxhc3RQb25nOiBEYXRlIHwgbnVsbDtcclxuICBzZW5kOiAoZGF0YTogYW55KSA9PiB2b2lkO1xyXG59XHJcblxyXG4vLyBDcmVhdGUgdGhlIGNvbnRleHQgd2l0aCBkZWZhdWx0IHZhbHVlc1xyXG5jb25zdCBXZWJTb2NrZXRDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxXZWJTb2NrZXRDb250ZXh0VHlwZT4oe1xyXG4gIGlzQ29ubmVjdGVkOiBmYWxzZSxcclxuICBsYXN0UG9uZzogbnVsbCxcclxuICBzZW5kOiAoKSA9PiB7fSwgLy8gRW1wdHkgZnVuY3Rpb24gYXMgcGxhY2Vob2xkZXJcclxufSk7XHJcblxyXG5pbnRlcmZhY2UgV2ViU29ja2V0UHJvdmlkZXJQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuLyoqXHJcbiAqIFdlYlNvY2tldCBQcm92aWRlclxyXG4gKiBNYW5hZ2VzIFdlYlNvY2tldCBjb25uZWN0aW9uIGFuZCBtZXNzYWdlIGhhbmRsaW5nXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gV2ViU29ja2V0UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBXZWJTb2NrZXRQcm92aWRlclByb3BzKSB7XHJcbiAgY29uc3QgW3NvY2tldCwgc2V0U29ja2V0XSA9IHVzZVN0YXRlPFdlYlNvY2tldCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpc0Nvbm5lY3RlZCwgc2V0SXNDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtsYXN0UG9uZywgc2V0TGFzdFBvbmddID0gdXNlU3RhdGU8RGF0ZSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtyZWNvbm5lY3RBdHRlbXB0LCBzZXRSZWNvbm5lY3RBdHRlbXB0XSA9IHVzZVN0YXRlKDApO1xyXG4gIFxyXG4gIGNvbnN0IGlzQXV0aGVudGljYXRlZCA9IHVzZUF1dGhTdG9yZShzdGF0ZSA9PiBzdGF0ZS5pc0F1dGhlbnRpY2F0ZWQpO1xyXG4gIGNvbnN0IHVzZXIgPSB1c2VBdXRoU3RvcmUoc3RhdGUgPT4gc3RhdGUudXNlcik7XHJcbiAgY29uc3QgaW5jcmVtZW50VW5yZWFkTm90aWZpY2F0aW9ucyA9IHVzZVVJU3RvcmUoc3RhdGUgPT4gc3RhdGUuaW5jcmVtZW50VW5yZWFkTm90aWZpY2F0aW9uc0NvdW50KTtcclxuICBcclxuICAvLyBDb25uZWN0aW9uIGFuZCByZWNvbm5lY3Rpb24gbG9naWNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gT25seSBjb25uZWN0IGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZFxyXG4gICAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQgfHwgIXVzZXIpIHtcclxuICAgICAgaWYgKHNvY2tldCkge1xyXG4gICAgICAgIHNvY2tldC5jbG9zZSgpO1xyXG4gICAgICAgIHNldFNvY2tldChudWxsKTtcclxuICAgICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAvLyBXZWJTb2NrZXQgc2VydmVyIFVSTCBmcm9tIGVudmlyb25tZW50XHJcbiAgICBjb25zdCB3c1VybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1dTX1VSTCB8fCBgJHt3aW5kb3cubG9jYXRpb24ucHJvdG9jb2wgPT09ICdodHRwczonID8gJ3dzczonIDogJ3dzOid9Ly8ke3dpbmRvdy5sb2NhdGlvbi5ob3N0fS93c2A7XHJcbiAgICBcclxuICAgIC8vIEluaXRpYWxpemUgV2ViU29ja2V0XHJcbiAgICBjb25zdCB3cyA9IG5ldyBXZWJTb2NrZXQoYCR7d3NVcmx9P3VzZXJJZD0ke3VzZXIuaWR9YCk7XHJcbiAgICBcclxuICAgIC8vIFNldCB1cCBXZWJTb2NrZXQgZXZlbnQgaGFuZGxlcnNcclxuICAgIHdzLm9ub3BlbiA9ICgpID0+IHtcclxuICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBjb25uZWN0ZWQnKTtcclxuICAgICAgc2V0SXNDb25uZWN0ZWQodHJ1ZSk7XHJcbiAgICAgIHNldFJlY29ubmVjdEF0dGVtcHQoMCk7IC8vIFJlc2V0IHJlY29ubmVjdCBhdHRlbXB0c1xyXG4gICAgICBcclxuICAgICAgLy8gU2VuZCBhbiBpbml0aWFsIGF1dGhlbnRpY2F0aW9uIG1lc3NhZ2VcclxuICAgICAgd3Muc2VuZChKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgdHlwZTogJ2F1dGgnLFxyXG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgfSkpO1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgd3Mub25jbG9zZSA9IChldmVudCkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGRpc2Nvbm5lY3RlZDonLCBldmVudC5jb2RlLCBldmVudC5yZWFzb24pO1xyXG4gICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBJbXBsZW1lbnQgcmVjb25uZWN0aW9uIGxvZ2ljIHdpdGggZXhwb25lbnRpYWwgYmFja29mZlxyXG4gICAgICBpZiAoaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgICAgY29uc3QgYmFja29mZlRpbWUgPSBNYXRoLm1pbigxMDAwICogTWF0aC5wb3coMiwgcmVjb25uZWN0QXR0ZW1wdCksIDMwMDAwKTsgLy8gTWF4IDMwIHNlY29uZHNcclxuICAgICAgICBjb25zb2xlLmxvZyhgUmVjb25uZWN0aW5nIGluICR7YmFja29mZlRpbWV9bXMgKGF0dGVtcHQgJHtyZWNvbm5lY3RBdHRlbXB0ICsgMX0pYCk7XHJcbiAgICAgICAgXHJcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBzZXRSZWNvbm5lY3RBdHRlbXB0KHByZXYgPT4gcHJldiArIDEpO1xyXG4gICAgICAgIH0sIGJhY2tvZmZUaW1lKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICAgIFxyXG4gICAgd3Mub25lcnJvciA9IChlcnJvcikgPT4ge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdXZWJTb2NrZXQgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgfTtcclxuICAgIFxyXG4gICAgd3Mub25tZXNzYWdlID0gKGV2ZW50KSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IEpTT04ucGFyc2UoZXZlbnQuZGF0YSk7XHJcbiAgICAgICAgaGFuZGxlTWVzc2FnZShtZXNzYWdlKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIFdlYlNvY2tldCBtZXNzYWdlOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICAgIFxyXG4gICAgLy8gU3RvcmUgdGhlIFdlYlNvY2tldCBpbnN0YW5jZVxyXG4gICAgc2V0U29ja2V0KHdzKTtcclxuICAgIFxyXG4gICAgLy8gQ2xlYW4gdXAgb24gdW5tb3VudFxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd3MuY2xvc2UoKTtcclxuICAgIH07XHJcbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgdXNlciwgcmVjb25uZWN0QXR0ZW1wdF0pO1xyXG4gIFxyXG4gIC8vIEhhbmRsZSBkaWZmZXJlbnQgdHlwZXMgb2YgV2ViU29ja2V0IG1lc3NhZ2VzXHJcbiAgY29uc3QgaGFuZGxlTWVzc2FnZSA9IChtZXNzYWdlOiBhbnkpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdXZWJTb2NrZXQgbWVzc2FnZSByZWNlaXZlZDonLCBtZXNzYWdlKTtcclxuICAgIFxyXG4gICAgLy8gSGFuZGxlIGRpZmZlcmVudCBtZXNzYWdlIHR5cGVzXHJcbiAgICBzd2l0Y2ggKG1lc3NhZ2UudHlwZSkge1xyXG4gICAgICBjYXNlICdwb25nJzpcclxuICAgICAgICBzZXRMYXN0UG9uZyhuZXcgRGF0ZSgpKTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgXHJcbiAgICAgIGNhc2UgJ25vdGlmaWNhdGlvbic6XHJcbiAgICAgICAgLy8gV2hlbiBhIG5ldyBub3RpZmljYXRpb24gYXJyaXZlc1xyXG4gICAgICAgIGluY3JlbWVudFVucmVhZE5vdGlmaWNhdGlvbnMoKTtcclxuICAgICAgICAvLyBJbnZhbGlkYXRlIG5vdGlmaWNhdGlvbnMgcXVlcnkgdG8gcmVmZXRjaFxyXG4gICAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbm90aWZpY2F0aW9ucyddIH0pO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBcclxuICAgICAgY2FzZSAnY2FsbF9zdGF0dXNfdXBkYXRlZCc6XHJcbiAgICAgICAgLy8gSW52YWxpZGF0ZSBjYWxsIGxvZ3MgdG8gcmVmcmVzaCB0aGUgbGlzdFxyXG4gICAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnY2FsbC1sb2dzJ10gfSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlICdjcmVkaXRfdXBkYXRlZCc6XHJcbiAgICAgICAgLy8gVXBkYXRlIGNyZWRpdCBiYWxhbmNlIGluIFVJXHJcbiAgICAgICAgcXVlcnlDbGllbnQuaW52YWxpZGF0ZVF1ZXJpZXMoeyBxdWVyeUtleTogWydjcmVkaXRzJ10gfSk7XHJcbiAgICAgICAgYnJlYWs7XHJcbiAgICAgIFxyXG4gICAgICBjYXNlICduZXdfbnVtYmVyX2Fzc2lnbmVkJzpcclxuICAgICAgICAvLyBSZWZyZXNoIHBob25lIG51bWJlcnMgbGlzdFxyXG4gICAgICAgIHF1ZXJ5Q2xpZW50LmludmFsaWRhdGVRdWVyaWVzKHsgcXVlcnlLZXk6IFsnbnVtYmVycyddIH0pO1xyXG4gICAgICAgIGJyZWFrO1xyXG4gICAgICBcclxuICAgICAgZGVmYXVsdDpcclxuICAgICAgICBjb25zb2xlLmxvZygnVW5oYW5kbGVkIFdlYlNvY2tldCBtZXNzYWdlIHR5cGU6JywgbWVzc2FnZS50eXBlKTtcclxuICAgIH1cclxuICB9O1xyXG4gIFxyXG4gIC8vIEZ1bmN0aW9uIHRvIHNlbmQgYSBtZXNzYWdlIHRocm91Z2ggdGhlIFdlYlNvY2tldFxyXG4gIGNvbnN0IHNlbmQgPSAoZGF0YTogYW55KSA9PiB7XHJcbiAgICBpZiAoc29ja2V0ICYmIGlzQ29ubmVjdGVkKSB7XHJcbiAgICAgIHNvY2tldC5zZW5kKEpTT04uc3RyaW5naWZ5KGRhdGEpKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnNvbGUud2FybignQ2Fubm90IHNlbmQgbWVzc2FnZTogV2ViU29ja2V0IGlzIG5vdCBjb25uZWN0ZWQnKTtcclxuICAgIH1cclxuICB9O1xyXG4gIFxyXG4gIC8vIEtlZXAgV2ViU29ja2V0IGFsaXZlIHdpdGggcGluZy9wb25nIChpZiBzZXJ2ZXIgc3VwcG9ydHMgaXQpXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghaXNDb25uZWN0ZWQpIHJldHVybjtcclxuICAgIFxyXG4gICAgY29uc3QgcGluZ0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xyXG4gICAgICBpZiAoc29ja2V0ICYmIHNvY2tldC5yZWFkeVN0YXRlID09PSBXZWJTb2NrZXQuT1BFTikge1xyXG4gICAgICAgIHNlbmQoeyB0eXBlOiAncGluZycgfSk7XHJcbiAgICAgIH1cclxuICAgIH0sIDMwMDAwKTsgLy8gU2VuZCBwaW5nIGV2ZXJ5IDMwIHNlY29uZHNcclxuICAgIFxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgY2xlYXJJbnRlcnZhbChwaW5nSW50ZXJ2YWwpO1xyXG4gICAgfTtcclxuICB9LCBbc29ja2V0LCBpc0Nvbm5lY3RlZF0pO1xyXG4gIFxyXG4gIC8vIENvbnRleHQgdmFsdWVcclxuICBjb25zdCB2YWx1ZSA9IHtcclxuICAgIGlzQ29ubmVjdGVkLFxyXG4gICAgbGFzdFBvbmcsXHJcbiAgICBzZW5kLFxyXG4gIH07XHJcbiAgXHJcbiAgcmV0dXJuIChcclxuICAgIDxXZWJTb2NrZXRDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvV2ViU29ja2V0Q29udGV4dC5Qcm92aWRlcj5cclxuICApO1xyXG59XHJcblxyXG4vLyBDdXN0b20gaG9vayB0byB1c2UgdGhlIFdlYlNvY2tldCBjb250ZXh0XHJcbmV4cG9ydCBjb25zdCB1c2VXZWJTb2NrZXQgPSAoKSA9PiB1c2VDb250ZXh0KFdlYlNvY2tldENvbnRleHQpO1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQXV0aFN0b3JlIiwidXNlVUlTdG9yZSIsInF1ZXJ5Q2xpZW50IiwiV2ViU29ja2V0Q29udGV4dCIsImlzQ29ubmVjdGVkIiwibGFzdFBvbmciLCJzZW5kIiwiV2ViU29ja2V0UHJvdmlkZXIiLCJjaGlsZHJlbiIsInNvY2tldCIsInNldFNvY2tldCIsInNldElzQ29ubmVjdGVkIiwic2V0TGFzdFBvbmciLCJyZWNvbm5lY3RBdHRlbXB0Iiwic2V0UmVjb25uZWN0QXR0ZW1wdCIsImlzQXV0aGVudGljYXRlZCIsInN0YXRlIiwidXNlciIsImluY3JlbWVudFVucmVhZE5vdGlmaWNhdGlvbnMiLCJpbmNyZW1lbnRVbnJlYWROb3RpZmljYXRpb25zQ291bnQiLCJjbG9zZSIsIndzVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1dTX1VSTCIsIndpbmRvdyIsImxvY2F0aW9uIiwicHJvdG9jb2wiLCJob3N0Iiwid3MiLCJXZWJTb2NrZXQiLCJpZCIsIm9ub3BlbiIsImNvbnNvbGUiLCJsb2ciLCJKU09OIiwic3RyaW5naWZ5IiwidHlwZSIsInVzZXJJZCIsIm9uY2xvc2UiLCJldmVudCIsImNvZGUiLCJyZWFzb24iLCJiYWNrb2ZmVGltZSIsIk1hdGgiLCJtaW4iLCJwb3ciLCJzZXRUaW1lb3V0IiwicHJldiIsIm9uZXJyb3IiLCJlcnJvciIsIm9ubWVzc2FnZSIsIm1lc3NhZ2UiLCJwYXJzZSIsImRhdGEiLCJoYW5kbGVNZXNzYWdlIiwiRGF0ZSIsImludmFsaWRhdGVRdWVyaWVzIiwicXVlcnlLZXkiLCJ3YXJuIiwicGluZ0ludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJyZWFkeVN0YXRlIiwiT1BFTiIsImNsZWFySW50ZXJ2YWwiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlV2ViU29ja2V0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./providers/WebSocketProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./stores/authStore.ts":
/*!*****************************!*\
  !*** ./stores/authStore.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthLoading: () => (/* binding */ useAuthLoading),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore),\n/* harmony export */   useIsAuthenticated: () => (/* binding */ useIsAuthenticated),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiClient */ \"(ssr)/./lib/apiClient.ts\");\n\n\n\n// Create the auth store\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        isAuthenticated: false,\n        user: null,\n        permissions: [],\n        isLoading: true,\n        login: (userData)=>{\n            set({\n                isAuthenticated: true,\n                user: userData,\n                isLoading: false\n            });\n            // Fetch permissions after login\n            get().fetchPermissions();\n        },\n        logout: async ()=>{\n            try {\n                // Call the logout API endpoint\n                await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.post(\"/auth/logout\");\n            } catch (error) {\n                console.error(\"Logout error:\", error);\n            } finally{\n                // Always clear local state, even if API call fails\n                set({\n                    isAuthenticated: false,\n                    user: null,\n                    permissions: [],\n                    isLoading: false\n                });\n            }\n        },\n        setLoading: (status)=>{\n            set({\n                isLoading: status\n            });\n        },\n        setUser: (userData)=>{\n            set({\n                user: userData\n            });\n        },\n        setPermissions: (permissions)=>{\n            set({\n                permissions\n            });\n        },\n        checkAuth: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const user = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/users/me\");\n                set({\n                    isAuthenticated: true,\n                    user,\n                    isLoading: false\n                });\n                // Fetch permissions after successful authentication\n                await get().fetchPermissions();\n                return true;\n            } catch (error) {\n                // If unauthorized or any other error, ensure logged out state\n                set({\n                    isAuthenticated: false,\n                    user: null,\n                    permissions: [],\n                    isLoading: false\n                });\n                return false;\n            }\n        },\n        fetchPermissions: async ()=>{\n            try {\n                const response = await _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/users/permissions\");\n                const permissions = response.data.permissions;\n                set({\n                    permissions\n                });\n                return permissions;\n            } catch (error) {\n                console.error(\"Error fetching permissions:\", error);\n                return [];\n            }\n        },\n        hasPermission: (permission)=>{\n            const { permissions } = get();\n            // Super admin has all permissions\n            if (permissions.includes(\"**\")) return true;\n            // Parse the permission string\n            const [resource, action, scope = \"any\"] = permission.split(\":\");\n            // Check for exact permission match\n            if (permissions.includes(permission)) return true;\n            // Check for wildcard permissions\n            if (permissions.includes(`${resource}:*`)) return true;\n            if (permissions.includes(`${resource}:*:${scope}`)) return true;\n            if (permissions.includes(`*:${action}`)) return true;\n            if (permissions.includes(`*:${action}:${scope}`)) return true;\n            if (permissions.includes(`${resource}:${action}:*`)) return true;\n            if (permissions.includes(`${resource}:${action}`)) return true;\n            return false;\n        },\n        hasAnyPermission: (permissionsToCheck)=>{\n            return permissionsToCheck.some((permission)=>get().hasPermission(permission));\n        },\n        hasAllPermissions: (permissionsToCheck)=>{\n            return permissionsToCheck.every((permission)=>get().hasPermission(permission));\n        }\n    }), {\n    name: \"callsaver-auth\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>sessionStorage),\n    // Only persist authentication status and user data, not loading state\n    partialize: (state)=>({\n            isAuthenticated: state.isAuthenticated,\n            user: state.user,\n            permissions: state.permissions\n        })\n}));\n// Export a selector to prevent unnecessary re-renders\nconst useUser = ()=>useAuthStore((state)=>state.user);\nconst useIsAuthenticated = ()=>useAuthStore((state)=>state.isAuthenticated);\nconst useAuthLoading = ()=>useAuthStore((state)=>state.isLoading);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./stores/authStore.ts\n");

/***/ }),

/***/ "(ssr)/./stores/uiStore.ts":
/*!***************************!*\
  !*** ./stores/uiStore.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotificationsPanelState: () => (/* binding */ useNotificationsPanelState),\n/* harmony export */   useSidebarState: () => (/* binding */ useSidebarState),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore),\n/* harmony export */   useUnreadNotificationsCount: () => (/* binding */ useUnreadNotificationsCount)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// Create the UI store\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        isSidebarCollapsed: false,\n        isNotificationsPanelOpen: false,\n        unreadNotificationsCount: 0,\n        toggleSidebar: ()=>{\n            set((state)=>({\n                    isSidebarCollapsed: !state.isSidebarCollapsed\n                }));\n        },\n        toggleNotificationsPanel: ()=>{\n            set((state)=>({\n                    isNotificationsPanelOpen: !state.isNotificationsPanelOpen,\n                    // Auto-clear unread count when opening the panel\n                    unreadNotificationsCount: state.isNotificationsPanelOpen ? state.unreadNotificationsCount : 0\n                }));\n        },\n        setUnreadNotificationsCount: (count)=>{\n            set({\n                unreadNotificationsCount: count\n            });\n        },\n        incrementUnreadNotificationsCount: ()=>{\n            set((state)=>({\n                    unreadNotificationsCount: state.unreadNotificationsCount + 1\n                }));\n        },\n        clearUnreadNotificationsCount: ()=>{\n            set({\n                unreadNotificationsCount: 0\n            });\n        }\n    }), {\n    name: \"callsaver-ui\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    // Only persist certain UI state that should be remembered between sessions\n    partialize: (state)=>({\n            isSidebarCollapsed: state.isSidebarCollapsed\n        })\n}));\n// Export selectors to prevent unnecessary re-renders\nconst useSidebarState = ()=>useUIStore((state)=>state.isSidebarCollapsed);\nconst useNotificationsPanelState = ()=>useUIStore((state)=>state.isNotificationsPanelOpen);\nconst useUnreadNotificationsCount = ()=>useUIStore((state)=>state.unreadNotificationsCount);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./stores/uiStore.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2750468ece7d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYWxsc2F2ZXI0Ly4vYXBwL2dsb2JhbHMuY3NzPzZhODYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyNzUwNDY4ZWNlN2RcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/components/DynamicImports.jsx":
/*!*******************************************!*\
  !*** ./app/components/DynamicImports.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   ConditionalNavbar: () => (/* binding */ e2),
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   PerformanceOptimizer: () => (/* binding */ e1),
/* harmony export */   WebVitalsInit: () => (/* binding */ e3),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\DynamicImports.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\DynamicImports.jsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\DynamicImports.jsx#PerformanceOptimizer`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\DynamicImports.jsx#ConditionalNavbar`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\DynamicImports.jsx#WebVitalsInit`);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\components\DynamicImports.jsx#default`));


/***/ }),

/***/ "(rsc)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ e0),
/* harmony export */   useLanguage: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\i18n\LanguageContext.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\i18n\LanguageContext.jsx#LanguageProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\i18n\LanguageContext.jsx#useLanguage`);


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./i18n/LanguageContext */ \"(rsc)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(rsc)/./app/providers/SessionProvider.jsx\");\n/* harmony import */ var _providers_AppProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../providers/AppProviders */ \"(rsc)/./providers/AppProviders.tsx\");\n/* harmony import */ var _components_DynamicImports__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/DynamicImports */ \"(rsc)/./app/components/DynamicImports.jsx\");\n\n\n\n\n\n\n// Import dynamically loaded components from the client component\n\nconst metadata = {\n    title: \"CallSaver - Never Miss A Customer Call Again\",\n    description: \"AI-Powered Call Management Platform\",\n    // Add additional metadata\n    openGraph: {\n        title: \"CallSaver - Never Miss A Customer Call Again\",\n        description: \"AI-Powered Call Management Platform\",\n        type: \"website\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    }\n};\n// Define viewport metadata separately (Next.js 14+ requirement)\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    themeColor: \"#0d0d17\"\n};\n// Define caching headers for better performance\nconst revalidate = 3600; // Revalidate at most once per hour\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\r\n            :root {\r\n              --main-padding-top: 0;\r\n            }\r\n          `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\r\n            document.addEventListener('DOMContentLoaded', function() {\r\n              setTimeout(() => {\r\n                document.querySelectorAll('.scroll-reveal').forEach(el => {\r\n                  el.style.opacity = '1';\r\n                  el.style.transform = 'translateY(0)';\r\n                });\r\n              }, 100);\r\n            });\r\n          `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className)} bg-[#0d0d17] min-h-screen overflow-x-hidden`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_3__.SessionProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AppProviders__WEBPACK_IMPORTED_MODULE_4__.AppProviders, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen flex flex-col relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicImports__WEBPACK_IMPORTED_MODULE_5__.ConditionalNavbar, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                        className: \"flex-grow relative\",\n                                        style: {\n                                            paddingTop: \"var(--main-padding-top)\"\n                                        },\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   SessionProvider: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSession: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx#SessionProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx#useSession`);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\app\providers\SessionProvider.jsx#default`));


/***/ }),

/***/ "(rsc)/./providers/AppProviders.tsx":
/*!************************************!*\
  !*** ./providers/AppProviders.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppProviders: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\providers\AppProviders.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\callsaver.app\frontend\providers\AppProviders.tsx#AppProviders`);


/***/ }),

/***/ "(ssr)/./app/i18n/locales/ar.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/ar.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"دع الذكاء الاصطناعي يتعامل مع مكالماتك،","line2":"أنت تتعامل مع الحياة."},"subtitle":"متصل بسلاسة. أنت بلا جهد. يقوم حل الرسائل القصيرة الذكي الخاص بنا بإدارة مكالماتك الفائتة حتى تتمكن من التركيز على ما يهم.","buttons":{"trial":"تسجيل الدخول/التسجيل","pricing":"عرض الأسعار"},"footer":{"poweredBy":"مشغل بواسطة","businesses":"+5000 شركة"}},"navbar":{"features":"المميزات","pricing":"الأسعار","testimonials":"الشهادات","signin":"تسجيل الدخول/التسجيل","languages":{"english":"الإنجليزية","german":"الألمانية","arabic":"العربية"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/de.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/de.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Lassen Sie KI Ihre Anrufe verwalten,","line2":"Sie genießen das Leben."},"subtitle":"Nahtlos verbunden. Mühelos Sie. Unsere intelligente SMS-Lösung verwaltet Ihre verpassten Anrufe, damit Sie sich auf das Wesentliche konzentrieren können.","buttons":{"trial":"Anmelden/Registrieren","pricing":"Preise anzeigen"},"footer":{"poweredBy":"betrieben von","businesses":"5000+ Unternehmen"}},"navbar":{"features":"Funktionen","pricing":"Preisgestaltung","testimonials":"Erfahrungsberichte","signin":"Anmelden/Registrieren","languages":{"english":"Englisch","german":"Deutsch","arabic":"Arabisch"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/en.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/en.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Let AI Handle Your Calls,","line2":"You Handle Life."},"subtitle":"Seamlessly Connected. Effortlessly You. Our intelligent SMS solution manages your missed calls so you can focus on what matters.","buttons":{"trial":"Try 7 Days Free Trial","pricing":"View Pricing"},"footer":{"poweredBy":"powered by","businesses":"5000+ businesses"}},"navbar":{"features":"Features","pricing":"Pricing","testimonials":"Testimonials","signin":"Sign In/Up","languages":{"english":"English","german":"German","arabic":"Arabic"}}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/cookie","vendor-chunks/asynckit","vendor-chunks/webidl-conversions","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();