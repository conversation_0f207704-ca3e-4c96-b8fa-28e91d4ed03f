"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_WebVitalsInit_jsx"],{

/***/ "(app-pages-browser)/./app/components/WebVitalsInit.jsx":
/*!******************************************!*\
  !*** ./app/components/WebVitalsInit.jsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WebVitalsInit; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(app-pages-browser)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n/**\n * Client component to initialize Web Vitals monitoring\n */ function WebVitalsInit() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Function to log web vitals metrics\n        const logWebVitals = (metric)=>{\n            // Log to console in development\n            if (true) {\n                console.log(\"Web Vitals: \".concat(metric.name, \" = \").concat(metric.value));\n            }\n            // Send to analytics in production\n            if (false) {}\n        };\n        // Initialize all web vitals metrics\n        (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(logWebVitals); // Cumulative Layout Shift\n        (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFID)(logWebVitals); // First Input Delay\n        (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(logWebVitals); // Largest Contentful Paint\n        (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(logWebVitals); // Time to First Byte\n        (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(logWebVitals); // Interaction to Next Paint\n    }, []);\n    // This component doesn't render anything\n    return null;\n}\n_s(WebVitalsInit, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = WebVitalsInit;\nvar _c;\n$RefreshReg$(_c, \"WebVitalsInit\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/WebVitalsInit.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/web-vitals/dist/web-vitals.js":
/*!****************************************************!*\
  !*** ./node_modules/web-vitals/dist/web-vitals.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLSThresholds: function() { return /* binding */ b; },\n/* harmony export */   FCPThresholds: function() { return /* binding */ L; },\n/* harmony export */   FIDThresholds: function() { return /* binding */ D; },\n/* harmony export */   INPThresholds: function() { return /* binding */ j; },\n/* harmony export */   LCPThresholds: function() { return /* binding */ U; },\n/* harmony export */   TTFBThresholds: function() { return /* binding */ X; },\n/* harmony export */   getCLS: function() { return /* binding */ S; },\n/* harmony export */   getFCP: function() { return /* binding */ w; },\n/* harmony export */   getFID: function() { return /* binding */ x; },\n/* harmony export */   getINP: function() { return /* binding */ Q; },\n/* harmony export */   getLCP: function() { return /* binding */ W; },\n/* harmony export */   getTTFB: function() { return /* binding */ Z; },\n/* harmony export */   onCLS: function() { return /* binding */ S; },\n/* harmony export */   onFCP: function() { return /* binding */ w; },\n/* harmony export */   onFID: function() { return /* binding */ x; },\n/* harmony export */   onINP: function() { return /* binding */ Q; },\n/* harmony export */   onLCP: function() { return /* binding */ W; },\n/* harmony export */   onTTFB: function() { return /* binding */ Z; }\n/* harmony export */ });\nvar e,n,t,i,r,a=-1,o=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(a=n.timeStamp,e(n))}),!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0]},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),i=\"navigate\";a>=0?i=\"back-forward-cache\":t&&(document.prerendering||u()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":t.type&&(i=t.type.replace(/_/g,\"-\")));return{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v3-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},d=function(e,n,t,i){var r,a;return function(o){n.value>=0&&(o||i)&&((a=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=a,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){var n=function(n){\"pagehide\"!==n.type&&\"hidden\"!==document.visibilityState||e(n)};addEventListener(\"visibilitychange\",n,!0),addEventListener(\"pagehide\",n,!0)},v=function(e){var n=!1;return function(t){n||(e(t),n=!0)}},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),o((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},C=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},L=[1800,3e3],w=function(e,n){n=n||{},C((function(){var t,i=E(),r=f(\"FCP\"),a=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(a.disconnect(),e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-u(),0),r.entries.push(e),t(!0)))}))}));a&&(t=d(e,r,L,n.reportAllChanges),o((function(i){r=f(\"FCP\"),t=d(e,r,L,n.reportAllChanges),l((function(){r.value=performance.now()-i.timeStamp,t(!0)}))})))}))},b=[.1,.25],S=function(e,n){n=n||{},w(v((function(){var t,i=f(\"CLS\",0),r=0,a=[],c=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=a[0],t=a[a.length-1];r&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(r+=e.value,a.push(e)):(r=e.value,a=[e])}})),r>i.value&&(i.value=r,i.entries=a,t())},u=s(\"layout-shift\",c);u&&(t=d(e,i,b,n.reportAllChanges),p((function(){c(u.takeRecords()),t(!0)})),o((function(){r=0,i=f(\"CLS\",0),t=d(e,i,b,n.reportAllChanges),l((function(){return t()}))})),setTimeout(t,0))})))},A={passive:!0,capture:!0},I=new Date,P=function(i,r){e||(e=r,n=i,t=new Date,k(removeEventListener),F())},F=function(){if(n>=0&&n<t-I){var r={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+n};i.forEach((function(e){e(r)})),i=[]}},M=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){P(e,n),r()},i=function(){r()},r=function(){removeEventListener(\"pointerup\",t,A),removeEventListener(\"pointercancel\",i,A)};addEventListener(\"pointerup\",t,A),addEventListener(\"pointercancel\",i,A)}(n,e):P(n,e)}},k=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,M,A)}))},D=[100,300],x=function(t,r){r=r||{},C((function(){var a,c=E(),u=f(\"FID\"),l=function(e){e.startTime<c.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),a(!0))},m=function(e){e.forEach(l)},h=s(\"first-input\",m);a=d(t,u,D,r.reportAllChanges),h&&p(v((function(){m(h.takeRecords()),h.disconnect()}))),h&&o((function(){var o;u=f(\"FID\"),a=d(t,u,D,r.reportAllChanges),i=[],n=-1,e=null,k(addEventListener),o=l,i.push(o),F()}))}))},B=0,R=1/0,H=0,N=function(e){e.forEach((function(e){e.interactionId&&(R=Math.min(R,e.interactionId),H=Math.max(H,e.interactionId),B=H?(H-R)/7+1:0)}))},O=function(){return r?B:performance.interactionCount||0},q=function(){\"interactionCount\"in performance||r||(r=s(\"event\",N,{type:\"event\",buffered:!0,durationThreshold:0}))},j=[200,500],_=0,z=function(){return O()-_},G=[],J={},K=function(e){var n=G[G.length-1],t=J[e.interactionId];if(t||G.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};J[i.id]=i,G.push(i)}G.sort((function(e,n){return n.latency-e.latency})),G.splice(10).forEach((function(e){delete J[e.id]}))}},Q=function(e,n){n=n||{},C((function(){var t;q();var i,r=f(\"INP\"),a=function(e){e.forEach((function(e){(e.interactionId&&K(e),\"first-input\"===e.entryType)&&(!G.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&K(e))}));var n,t=(n=Math.min(G.length-1,Math.floor(z()/50)),G[n]);t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())},c=s(\"event\",a,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});i=d(e,r,j,n.reportAllChanges),c&&(\"PerformanceEventTiming\"in window&&\"interactionId\"in PerformanceEventTiming.prototype&&c.observe({type:\"first-input\",buffered:!0}),p((function(){a(c.takeRecords()),r.value<0&&z()>0&&(r.value=0,r.entries=[]),i(!0)})),o((function(){G=[],_=O(),r=f(\"INP\"),i=d(e,r,j,n.reportAllChanges)})))}))},U=[2500,4e3],V={},W=function(e,n){n=n||{},C((function(){var t,i=E(),r=f(\"LCP\"),a=function(e){var n=e[e.length-1];n&&n.startTime<i.firstHiddenTime&&(r.value=Math.max(n.startTime-u(),0),r.entries=[n],t())},c=s(\"largest-contentful-paint\",a);if(c){t=d(e,r,U,n.reportAllChanges);var m=v((function(){V[r.id]||(a(c.takeRecords()),c.disconnect(),V[r.id]=!0,t(!0))}));[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,(function(){return setTimeout(m,0)}),!0)})),p(m),o((function(i){r=f(\"LCP\"),t=d(e,r,U,n.reportAllChanges),l((function(){r.value=performance.now()-i.timeStamp,V[r.id]=!0,t(!0)}))}))}}))},X=[800,1800],Y=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},Z=function(e,n){n=n||{};var t=f(\"TTFB\"),i=d(e,t,X,n.reportAllChanges);Y((function(){var r=c();if(r){var a=r.responseStart;if(a<=0||a>performance.now())return;t.value=Math.max(a-u(),0),t.entries=[r],i(!0),o((function(){t=f(\"TTFB\",0),(i=d(e,t,X,n.reportAllChanges))(!0)}))}}))};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/web-vitals/dist/web-vitals.js\n"));

/***/ })

}]);