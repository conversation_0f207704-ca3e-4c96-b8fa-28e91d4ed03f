"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_ToolsIntegrationSection_jsx";
exports.ids = ["_ssr_app_components_ToolsIntegrationSection_jsx"];
exports.modules = {

/***/ "(ssr)/./app/components/ToolsIntegrationSection.jsx":
/*!****************************************************!*\
  !*** ./app/components/ToolsIntegrationSection.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ToolsIntegrationSection = ()=>{\n    const integrations = [\n        {\n            name: \"Google Calendar\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg\"\n        },\n        {\n            name: \"Outlook\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/df/Microsoft_Office_Outlook_%282018%E2%80%93present%29.svg\"\n        },\n        {\n            name: \"Slack\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg\"\n        },\n        {\n            name: \"Salesforce\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg\"\n        },\n        {\n            name: \"Zapier\",\n            logo: \"https://cdn.worldvectorlogo.com/logos/zapier-1.svg\"\n        },\n        {\n            name: \"HubSpot\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/4/4d/HubSpot_Logo.svg\"\n        },\n        {\n            name: \"Zoom\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/1/11/Zoom_Logo_2022.svg\"\n        },\n        {\n            name: \"Shopify\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/0/0e/Shopify_logo_2018.svg\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-16 px-4 text-white scroll-reveal relative z-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-40 right-20 w-[30rem] h-[30rem] rounded-full bg-blue-600/5 blur-[100px] animate-pulse-slow\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-40 left-20 w-[30rem] h-[30rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow\",\n                style: {\n                    animationDelay: \"2s\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-4xl mx-auto mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"heading-lg mb-6 laser-gradient-text text-center\",\n                        \"data-text\": \"Tools & Integrations\",\n                        children: \"Tools & Integrations\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"subheading-text\",\n                        children: \"Seamlessly connects with the tools you already use, making it easy to incorporate CallSaver into your existing workflow without disruption.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-md p-8 md:p-10 rounded-xl border border-purple-500/20 max-w-5xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12\",\n                        children: integrations.map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center p-3 mb-4 hover:bg-white/90 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: integration.logo,\n                                                alt: `${integration.name} logo`,\n                                                fill: true,\n                                                sizes: \"(max-width: 768px) 64px, 80px\",\n                                                style: {\n                                                    objectFit: \"contain\"\n                                                },\n                                                quality: 90\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-300 text-center\",\n                                        children: integration.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: \"The bigger your ecosystem, the more powerful CallSaver becomes. Connect with all your favorite platforms through our API or integrations.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"#pricing\",\n                                    className: \"inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-white bg-purple-600 hover:bg-purple-700 transition duration-300 ease-in-out transform hover:scale-105\",\n                                    children: \"See All Integrations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToolsIntegrationSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ToolsIntegrationSection.jsx\n");

/***/ })

};
;