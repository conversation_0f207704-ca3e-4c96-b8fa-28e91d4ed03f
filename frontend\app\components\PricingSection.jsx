"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { loadStripe } from '@stripe/stripe-js';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/ssr'; // Use ssr client
import FallingIcons from './FallingIcons';
import { useSession } from '../providers/SessionProvider';
import { CheckIcon } from '@heroicons/react/24/outline'; // Import check icon

const stripePromise = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  ? loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)
  : null;

if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  console.error("Stripe publishable key not found in environment variables");
}

const PricingSection = () => {
  const [isAnnual, setIsAnnual] = useState(true);
  const [loadingPlan, setLoadingPlan] = useState(null);
  const [error, setError] = useState(null);
  const [countdown, setCountdown] = useState({
    days: 3,
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const router = useRouter();
  const { session } = useSession();

  // Add countdown timer functionality
  useEffect(() => {
    // Set end date to 3 days from now for the countdown
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);
    endDate.setHours(23, 59, 59, 999);

    const calculateTimeLeft = () => {
      const now = new Date();
      const difference = endDate - now;

      if (difference > 0) {
        setCountdown({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60)
        });
      }
    };

    // Calculate immediately
    calculateTimeLeft();

    // Set up interval to recalculate every second
    const timer = setInterval(calculateTimeLeft, 1000);

    // Clean up
    return () => clearInterval(timer);
  }, []);

  // Calculate discounted annual price (20% off monthly price * 12)
  const getAnnualPrice = (monthlyPrice) => {
    return Math.round(monthlyPrice * 12 * 0.8);
  };

  // Handle checkout process
  const handleCheckoutClick = async (planType) => {
    // Clear any previous errors
    setError(null);

    // Show loading state
    setLoadingPlan(planType);

    try {
      // Check if user is authenticated
      if (!session) {
        // Store their plan selection in localStorage for after login
        if (typeof window !== 'undefined') {
          localStorage.setItem('selectedPlan', planType);
          localStorage.setItem('billingInterval', isAnnual ? 'annual' : 'monthly');
        }
        router.push('/signin?returnTo=pricing');
        return; // Return early after redirecting
      }

      // Get the appropriate Stripe Price ID based on plan and billing interval
      let priceId;
      switch (planType) {
        case 'starter':
          priceId = isAnnual ? 'price_1R9J13E8oISCvrMNvbXbIki1' : 'price_1R9Iw0E8oISCvrMNWZaNiCks';
          break;
        case 'pro':
          priceId = isAnnual ? 'price_1R9J1bE8oISCvrMNIEDAap1h' : 'price_1R9IyRE8oISCvrMNCN3iOush';
          break;
        case 'enterprise':
          priceId = isAnnual ? 'price_1R9J6kE8oISCvrMNXJOn6cM9' : 'price_1R9IzME8oISCvrMNfx5fqOvo';
          break;
        default:
          throw new Error('Invalid plan type');
      }

      // Track analytics event if available
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'begin_checkout', {
          currency: 'USD',
          value: getPlanPrice(planType),
          items: [{
            id: planType,
            name: `${planType.charAt(0).toUpperCase() + planType.slice(1)} Plan - ${isAnnual ? 'Annual' : 'Monthly'}`,
            price: getPlanPrice(planType)
          }]
        });
      }

      // Create checkout session with trial information
      const response = await fetch('/api/billing/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          planType,
          billingInterval: isAnnual ? 'annual' : 'monthly',
          successUrl: window.location.origin + '/dashboard?checkout=success',
          cancelUrl: window.location.origin + '/pricing?checkout=cancelled'
        }),
        credentials: 'include', // Important for sending cookies
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 401) {
          // Handle unauthorized error
          router.push('/signin?returnTo=pricing');
          return;
        }
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Get Stripe instance
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error('Stripe failed to initialize. Please check your Stripe publishable key.');
      }

      // Redirect to checkout
      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId: data.sessionId
      });

      if (stripeError) {
        throw stripeError;
      }
    } catch (err) {
      console.error('Checkout error:', err);
      setError(err.message || 'Failed to start checkout. Please try again.');

      // If there's a Stripe initialization error, log additional details
      if (err.message.includes('Stripe failed to initialize')) {
        console.error('Stripe publishable key:', process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'Present' : 'Missing');
      }
    } finally {
      setLoadingPlan(null);
    }
  };

  // Helper function to get plan price for analytics
  const getPlanPrice = (planType) => {
    const plan = pricingPlans.find(p => p.id === planType);
    if (!plan) return 0;
    return isAnnual ? plan.annualPrice : plan.monthlyPrice;
  };

  // Pricing plans data - updated with clearer monthly benefit text for annual plans
  const pricingPlans = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small businesses just getting started',
      monthlyPrice: 99,
      annualPrice: getAnnualPrice(99),
      popular: false,
      features: [
        '1 AI Phone Line',
        isAnnual ? '200 AI SMS Chats each month (2,400 per year)' : '200 AI SMS Chats (to 200 different numbers)',
        '50 Voice AI minutes per month',
        'User Custom AI Training',
        'Basic Analytics Dashboard',
        'Email Support'
      ],
      color: 'from-blue-600 to-cyan-600',
      iconColor: 'text-blue-400',
      bgGradient: 'from-blue-900/20 to-cyan-900/20',
      buttonClass: 'bg-gradient-to-r from-blue-600 to-cyan-600',
      buttonText: 'Get Started',
      monthsFree: '2 months free'
    },
    {
      id: 'pro',
      name: 'Pro',
      description: 'Ideal for growing teams with higher call volumes',
      monthlyPrice: 149,
      annualPrice: getAnnualPrice(149),
      popular: true,
      features: [
        '2 AI Phone Lines',
        isAnnual ? '600 AI SMS Chats each month (7,200 per year)' : '600 AI SMS Chats (to 600 different numbers)',
        '200 Voice AI minutes per month',
        'User Custom AI Training',
        'Advanced Analytics & Reporting',
        'Unlimited Custom AI Responses',
        'Priority Support',
        'Team Collaboration Tools',
        'API Access for External Integrations'
      ],
      color: 'from-purple-600 to-indigo-600',
      iconColor: 'text-purple-400',
      bgGradient: 'from-purple-900/20 to-indigo-900/20',
      buttonClass: 'gradient-button', // Assuming this class provides a gradient
      buttonText: 'Upgrade to Pro',
      monthsFree: '2 months free'
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Customized solutions for large-scale operations',
      monthlyPrice: 499,
      annualPrice: getAnnualPrice(499),
      popular: false,
      features: [
        '5 AI Phone Lines',
        isAnnual ? '2,500 AI SMS Chats each month (30,000 per year)' : '2,500 AI SMS Chats (to 2,500 different numbers)',
        '1,000 Voice AI minutes per month',
        'User Custom AI Training',
        'Dedicated Account Manager',
        'Premium 24/7 Support',
        'Advanced Security & Data Encryption',
        'Full AI Customization (Specialized Workflows, Intent Recognition, etc.)',
        'Direct Callsaver Developer Integration (for connecting a special user database or third-party software)',
        'SLA Guarantees & Custom AI Compliance Features'
      ],
      color: 'from-pink-600 to-rose-600',
      iconColor: 'text-pink-400',
      bgGradient: 'from-pink-900/20 to-rose-900/20',
      buttonClass: 'bg-gradient-to-r from-pink-600 to-rose-600',
      buttonText: 'Get Enterprise',
      monthsFree: '2 months free'
    }
  ];

  // Animation variants for cards only (not prices)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    },
    hover: { // Enhanced hover effect
      y: -10, // Slightly less lift
      scale: 1.03, // Slightly more scale
      boxShadow: "0px 20px 40px rgba(139, 92, 246, 0.15), 0 0 15px rgba(139, 92, 246, 0.1)", // Brighter shadow
      borderColor: "rgba(167, 139, 250, 0.5)", // Highlight border
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  return (
    <section id="pricing" data-section="pricing" className="relative py-16 overflow-hidden">
      {/* Falling icons background */}
      <FallingIcons />

      {/* Enhanced Background Ambient Lighting */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-60 -left-60 w-[40rem] h-[40rem] bg-gradient-radial from-purple-600/15 via-transparent to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute -bottom-60 -right-60 w-[40rem] h-[40rem] bg-gradient-radial from-pink-600/15 via-transparent to-transparent rounded-full blur-3xl animate-pulse-slow-delay-1-5"></div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[50rem] h-[50rem] bg-gradient-radial from-indigo-600/5 via-transparent to-transparent rounded-full blur-3xl animate-pulse-slow-delay-0-5"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-14">
          <h2 className="heading-lg mb-6 laser-gradient-text" data-text="Simple, Transparent Pricing">
            Simple, Transparent Pricing
          </h2>
          <p className="subheading-text max-w-3xl mx-auto mb-6">
            Choose the plan that fits your business needs with our all-inclusive pricing. No hidden fees, no surprises.
          </p>

          {/* Payment methods banner */}
          <div className="max-w-3xl mx-auto mb-8 p-3 bg-white/5 backdrop-blur-md rounded-lg border border-gray-800">
            <div className="flex flex-col md:flex-row justify-center items-center gap-4">
              <span className="text-sm text-gray-300">Multiple payment options available:</span>
              <div className="flex items-center space-x-6">
                <div className="flex items-center" title="Credit Card">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  <span className="text-sm text-gray-300">Credit Card</span>
                </div>
                <div className="flex items-center" title="Apple Pay">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-300 mr-1" viewBox="0 0 24 24">
                    <path d="M14.94 5.19A4.38 4.38 0 0 0 16.31 2a4.38 4.38 0 0 0-3 1.52 4.09 4.09 0 0 0-1.32 3.08 3.6 3.6 0 0 0 2.95-1.41M16.08 7c-1.49-.06-2.75.84-3.46.84s-1.8-.82-3-.8a4.52 4.52 0 0 0-3.82 2.3c-1.63 2.83-.42 7 1.17 9.28.78 1.13 1.7 2.38 2.92 2.34s1.62-.78 3-.78 1.84.78 3.11.75 2.09-1.16 2.87-2.29a10.68 10.68 0 0 0 1.31-2.67A4.45 4.45 0 0 1 18 14.66a4.47 4.47 0 0 1-2.74-4.24 4.55 4.55 0 0 1 2.17-3.79 4.48 4.48 0 0 0-1.35-.63" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                  </svg>
                  <span className="text-sm text-gray-300">Apple Pay</span>
                </div>
                <div className="flex items-center" title="PayPal">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400 mr-1" viewBox="0 0 24 24">
                    <path d="M19.5 7.5h2.25A.75.75 0 0022.5 6.75v-3a.75.75 0 00-.75-.75H19.5m0 4.5H4.875c-.621 0-1.125.504-1.125 1.125v9.75c0 .621.504 1.125 1.125 1.125h10.75c.621 0 1.125-.504 1.125-1.125V8.625M19.5 7.5H4.875a1.125 1.125 0 00-1.125 1.125m0 0v1.5c0 .621.504 1.125 1.125 1.125m0 0h10.75m-10.75 0v10.5a1.125 1.125 0 001.125 1.125m12.375-1.125v-10.5M6.75 17.25h3.75m-3.75 1.5h3.75M6.75 15h.008v.008H6.75V15zm0 1.5h.008v.008H6.75v-.008zm0 1.5h.008v.008H6.75v-.008z" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                  </svg>
                  <span className="text-sm text-gray-300">PayPal</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Special Offer Banner with Countdown */}
          <div className="max-w-xl mx-auto mb-10 overflow-hidden rounded-lg">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-1">
              <div className="bg-gray-900 px-6 py-4 rounded-sm">
                <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-3">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-300 flex-shrink-0 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <span className="font-bold text-yellow-300">Limited Time Offer:</span>
                      <span className="ml-2 text-white">Save 20% with annual billing</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm text-gray-300">Offer ends in:</span>
                  </div>
                </div>

                {/* Countdown Timer */}
                <div className="flex justify-center items-center gap-3 text-center">
                  <div className="bg-gray-800 p-2 rounded-md w-16">
                    <div className="text-xl font-bold text-white">{countdown.days}</div>
                    <div className="text-xs text-gray-400">Days</div>
                  </div>
                  <div className="bg-gray-800 p-2 rounded-md w-16">
                    <div className="text-xl font-bold text-white">{countdown.hours}</div>
                    <div className="text-xs text-gray-400">Hours</div>
                  </div>
                  <div className="bg-gray-800 p-2 rounded-md w-16">
                    <div className="text-xl font-bold text-white">{countdown.minutes}</div>
                    <div className="text-xs text-gray-400">Minutes</div>
                  </div>
                  <div className="bg-gray-800 p-2 rounded-md w-16">
                    <div className="text-xl font-bold text-white">{countdown.seconds}</div>
                    <div className="text-xs text-gray-400">Seconds</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="max-w-xs mx-auto mb-10">
            <div className="p-1 bg-white/10 rounded-full">
              <div className="flex">
                <button
                  onClick={() => setIsAnnual(false)}
                  className={`w-1/2 py-2 text-sm font-medium rounded-full transition-all duration-300 ${
                    !isAnnual
                      ? "bg-purple-600 text-white shadow-md"
                      : "text-gray-300 hover:text-white"
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setIsAnnual(true)}
                  className={`w-1/2 py-2 text-sm font-medium rounded-full transition-all duration-300 ${
                    isAnnual
                      ? "bg-purple-600 text-white shadow-md relative"
                      : "text-gray-300 hover:text-white"
                  }`}
                >
                  Annual{" "}
                  <span className="text-xs font-normal ml-1 text-green-400">
                    Save 20%
                  </span>
                  {!isAnnual && (
                    <span className="absolute -top-2 -right-2 flex h-4 w-4">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-4 w-4 bg-green-500"></span>
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Annual Savings Breakdown - Only show when not on annual plan */}
          {!isAnnual && (
            <div className="max-w-xl mx-auto mb-8 p-4 border border-green-500/30 rounded-lg bg-green-900/10">
              <h3 className="text-center text-lg font-semibold text-white mb-3">Save with Annual Billing</h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                {pricingPlans.map((plan) => {
                  const monthlyCost = plan.monthlyPrice * 12;
                  const annualCost = plan.annualPrice;
                  const savings = monthlyCost - annualCost;

                  return (
                    <div key={`savings-${plan.id}`}>
                      <p className="text-sm font-medium text-white">{plan.name}</p>
                      <p className="text-xs text-gray-300">
                        <span className="line-through">${monthlyCost}</span>{" "}
                        <span className="text-white font-medium">${annualCost}</span>
                      </p>
                      <p className="text-xs text-green-400 font-medium">Save ${savings}/year</p>
                    </div>
                  );
                })}
              </div>
              <div className="mt-3 text-center">
                <button
                  onClick={() => setIsAnnual(true)}
                  className="text-sm py-1 px-3 bg-green-500/20 hover:bg-green-500/30 text-green-300 rounded-md transition-colors"
                >
                  Switch to Annual →
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-8 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
            <p className="text-red-400 text-center">{error}</p>
          </div>
        )}

        {/* Pricing Cards */}
        <motion.div
          id="pricing-cards"
          data-section="pricing-cards"
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {pricingPlans.map((plan) => (
            <motion.div
              key={plan.id}
              className={`relative bg-gray-900/80 backdrop-blur-sm border border-purple-500/20 rounded-2xl overflow-hidden transition-all duration-300 ${
                plan.popular
                  ? 'lg:scale-105 ring-2 ring-purple-500 shadow-lg shadow-purple-500/20 z-10 animate-pulse-border-slow' // Added slow pulse animation
                  : 'shadow-md shadow-black/20'
              }`}
              variants={cardVariants}
              whileHover="hover"
            >
              {/* Popular badge */}
              {plan.popular && (
                <div className="absolute top-0 right-0">
                  <div
                    className="py-1.5 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs font-bold uppercase tracking-wider rotate-45 transform translate-x-[30%] translate-y-[40%] shadow-lg"
                  >
                    Best Value
                  </div>
                </div>
              )}

              {/* Card Header */}
              <div className={`p-8 bg-gradient-to-br ${plan.bgGradient} flex flex-col items-start justify-between min-h-[220px]`}>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">{plan.name}</h3>
                  <p className="text-sm text-gray-300">{plan.description}</p>
                </div>

                <div className="w-full">
                  <div className="mt-4 flex items-end">
                    <motion.span
                      className="text-5xl font-bold text-white"
                      key={isAnnual ? `annual-${plan.id}` : `monthly-${plan.id}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                    >
                      ${isAnnual ? plan.annualPrice : plan.monthlyPrice}
                    </motion.span>
                    <span className="text-lg text-gray-300 ml-2 mb-1">
                      /{isAnnual ? 'year' : 'month'}
                    </span>
                  </div>

                  {isAnnual && (
                    <p className="mt-2 text-sm text-green-400 font-medium flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {plan.monthsFree}
                    </p>
                  )}

                  {/* Monthly price comparison if on annual */}
                  {isAnnual && (
                    <p className="mt-1 text-xs text-gray-400">
                      That's just ${Math.round(plan.annualPrice / 12)} per month
                    </p>
                  )}
                </div>
              </div>

              {/* Feature List */}
              <div className="p-8">
                <ul className="space-y-4">
                  {plan.features.map((feature, index) => {
                    // Check if this is a Voice AI feature to highlight it
                    const isVoiceFeature = feature.toLowerCase().includes('voice ai');

                    return (
                      <li key={index} className={`flex items-start ${isVoiceFeature ? 'bg-purple-900/20 p-2 rounded-md -mx-2' : ''}`}>
                        <CheckIcon className={`h-5 w-5 ${isVoiceFeature ? 'text-purple-400' : plan.iconColor} mr-2 flex-shrink-0 mt-0.5`} />
                        <span className={`${isVoiceFeature ? 'text-white font-medium' : 'text-gray-300'}`}>
                          {feature}
                          {isVoiceFeature && (
                            <span className="ml-2 text-xs bg-purple-500/30 px-1.5 py-0.5 rounded-full text-purple-200">NEW</span>
                          )}
                        </span>
                      </li>
                    );
                  })}
                </ul>

                {/* Price Guarantee */}
                <div className="mt-6 border-t border-gray-800 pt-4">
                  {!isAnnual ? (
                    <>
                      <p className="text-xs text-gray-400 flex items-center font-medium">
                        <CheckIcon className="w-3 h-3 mr-1 text-green-400" />
                        Cancel anytime
                      </p>
                      <p className="text-xs text-gray-400 flex items-center mt-1">
                        <CheckIcon className="w-3 h-3 mr-1 text-green-400" />
                        14-day money back guarantee
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-xs text-gray-400 flex items-center">
                        <CheckIcon className="w-3 h-3 mr-1 text-green-400" />
                        No credit card required to start
                      </p>
                      <p className="text-xs text-gray-400 flex items-center mt-1">
                        <CheckIcon className="w-3 h-3 mr-1 text-green-400" />
                        14-day money back guarantee
                      </p>
                    </>
                  )}
                </div>
              </div>

              {/* Update the button to include loading state and onClick handler */}
              <div className="p-6 border-t border-gray-800">
                <button
                  onClick={() => handleCheckoutClick(plan.id)}
                  disabled={loadingPlan === plan.id}
                  className={`w-full py-3 px-6 rounded-lg text-white font-semibold transition-all duration-300 transform hover:scale-105 ${
                    plan.buttonClass
                  } ${
                    loadingPlan === plan.id ? 'opacity-75 cursor-not-allowed' : 'hover:opacity-90 hover:shadow-lg'
                  }`}
                >
                  {loadingPlan === plan.id ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </div>
                  ) : (
                    <>
                      {plan.buttonText}
                    </>
                  )}
                </button>

                {/* Payment methods indicator */}
                <div className="mt-4 flex items-center justify-center">
                  <div className="text-xs text-gray-400 flex flex-col items-center">
                    <span className="mb-2">Payment options:</span>
                    <div className="flex space-x-3">
                      <div className="w-8 h-5 bg-white rounded flex items-center justify-center" title="Credit Card">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-gray-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>
                      </div>
                      <div className="w-8 h-5 bg-white rounded flex items-center justify-center" title="Apple Pay">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24">
                          <path d="M14.94 5.19A4.38 4.38 0 0 0 16.31 2a4.38 4.38 0 0 0-3 1.52 4.09 4.09 0 0 0-1.32 3.08 3.6 3.6 0 0 0 2.95-1.41M16.08 7c-1.49-.06-2.75.84-3.46.84s-1.8-.82-3-.8a4.52 4.52 0 0 0-3.82 2.3c-1.63 2.83-.42 7 1.17 9.28.78 1.13 1.7 2.38 2.92 2.34s1.62-.78 3-.78 1.84.78 3.11.75 2.09-1.16 2.87-2.29a10.68 10.68 0 0 0 1.31-2.67A4.45 4.45 0 0 1 18 14.66a4.47 4.47 0 0 1-2.74-4.24 4.55 4.55 0 0 1 2.17-3.79 4.48 4.48 0 0 0-1.35-.63" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                        </svg>
                      </div>
                      <div className="w-8 h-5 bg-white rounded flex items-center justify-center" title="PayPal">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" width="512" height="512">
                          <path d="M18.405 7.883a3.909 3.909 0 0 0-.845-3.041C16.727 3.878 15.372 3.5 13.605 3.5H7.697a.5.5 0 0 0-.494.418L5.02 16.421a.5.5 0 0 0 .494.579h3.03l-.173.978a.5.5 0 0 0 .493.522h2.701a.5.5 0 0 0 .494-.418l.57-3.254a.5.5 0 0 1 .494-.418h.79c2.422 0 4.31-.53 5.53-1.643 1.19-1.092 1.806-2.57 1.806-4.422 0-.213-.011-.425-.032-.635a.105.105 0 0 0-.019-.069c-.011-.017-.03-.029-.051-.035-.018-.006-.037-.008-.056-.006-.019.001-.037.007-.053.017" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Free Trial Section */}
        <div className="mt-16 max-w-4xl mx-auto">
          {/* Updated Trust signals section */}
          <div
            className="mb-8 text-center bg-gray-900/60 backdrop-blur-sm border border-gray-800 rounded-xl p-6 shadow-lg"
          >
            <div className="flex flex-col md:flex-row items-center justify-center gap-4 mb-3">
              <div className="flex items-center">
                <svg className="h-6 w-6 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-white font-medium">Start Today</span>
              </div>
              <div className="h-5 border-l border-gray-700 hidden md:block"></div>
              <div className="flex items-center">
                <svg className="h-6 w-6 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
                <span className="text-white font-medium">Multiple Payment Options</span>
              </div>
              <div className="h-5 border-l border-gray-700 hidden md:block"></div>
              <div className="flex items-center">
                <svg className="h-6 w-6 text-purple-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span className="text-white font-medium">14-Day Money Back</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
