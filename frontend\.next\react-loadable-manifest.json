{"app\\components\\DynamicImports.jsx -> ./ConditionalNavbar": {"id": "app\\components\\DynamicImports.jsx -> ./ConditionalNavbar", "files": ["static/chunks/_app-pages-browser_app_components_ConditionalNavbar_jsx.js"]}, "app\\components\\DynamicImports.jsx -> ./ErrorBoundary": {"id": "app\\components\\DynamicImports.jsx -> ./ErrorBoundary", "files": ["static/chunks/_app-pages-browser_app_components_ErrorBoundary_jsx.js"]}, "app\\components\\DynamicImports.jsx -> ./PerformanceOptimizer": {"id": "app\\components\\DynamicImports.jsx -> ./PerformanceOptimizer", "files": ["static/chunks/_app-pages-browser_app_components_PerformanceOptimizer_jsx.js"]}, "app\\components\\DynamicImports.jsx -> ./WebVitalsInit": {"id": "app\\components\\DynamicImports.jsx -> ./WebVitalsInit", "files": ["static/chunks/_app-pages-browser_app_components_WebVitalsInit_jsx.js"]}, "app\\page.js -> ./components/CallPreviewBox": {"id": "app\\page.js -> ./components/CallPreviewBox", "files": ["static/chunks/_app-pages-browser_app_components_CallPreviewBox_jsx.js"]}, "app\\page.js -> ./components/FeatureCards": {"id": "app\\page.js -> ./components/FeatureCards", "files": ["static/chunks/_app-pages-browser_app_components_FeatureCards_jsx.js"]}, "app\\page.js -> ./components/Footer": {"id": "app\\page.js -> ./components/Footer", "files": ["static/chunks/_app-pages-browser_app_components_Footer_jsx.js"]}, "app\\page.js -> ./components/TestimonialsSection": {"id": "app\\page.js -> ./components/TestimonialsSection", "files": ["static/chunks/_app-pages-browser_app_components_TestimonialsSection_jsx.js"]}, "app\\page.js -> ./components/ToolsIntegrationSection": {"id": "app\\page.js -> ./components/ToolsIntegrationSection", "files": ["static/chunks/_app-pages-browser_app_components_ToolsIntegrationSection_jsx.js"]}, "app\\page.js -> lottie-react": {"id": "app\\page.js -> lottie-react", "files": ["static/chunks/_app-pages-browser_node_modules_lottie-react_build_index_umd_js.js"]}, "node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch": {"id": "node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch", "files": []}, "node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch": {"id": "node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch", "files": []}, "node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> ws": {"id": "node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> ws", "files": ["static/chunks/_app-pages-browser_node_modules_ws_browser_js.js"]}, "node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsComponent/OHEVZFKG.js": {"id": "node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsComponent/OHEVZFKG.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsComponent_OHEVZFKG_js.js"]}, "node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsPanelComponent/ZXCTK5ZC.js": {"id": "node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsPanelComponent/ZXCTK5ZC.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsPanelComponent_ZXCTK5ZC_js.js"]}}